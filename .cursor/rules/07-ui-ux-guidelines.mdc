---
description: 
globs: 
alwaysApply: true
---
# 🎨 UI/UX设计与开发规范

本项目遵循现代化设计原则，追求简洁、优雅、用户友好的界面体验。每一个组件和页面都应当体现专业水准的设计品质。

## 📱 设计核心理念 (Dribbble Style)

### ✨ 极简主义 (Minimalism)
- **减少视觉噪音**：每个元素都有明确目的，移除多余装饰
- **专注内容核心**：突出主要信息，弱化次要信息
- **留白艺术**：合理使用空白空间，让界面呼吸感更强
- **信息层次**：通过字体大小、颜色、位置建立清晰的视觉层次

### 🎯 卡片设计 (Card-Based Design)
- **现代扁平化**：避免过度装饰，使用简洁的卡片容器
- **统一圆角**：标准圆角值 16-24rpx，营造柔和现代感
- **微妙阴影**：使用轻量级阴影增加层次，避免厚重感
- **内容分组**：相关信息组织在同一卡片内，提升可读性

### 🌊 微交互 (Micro-interactions)
- **流畅动画**：使用 `cubic-bezier(0.25, 0.46, 0.45, 0.94)` 等自然缓动
- **即时反馈**：按压、悬停状态要有明显但不突兀的反馈
- **状态转换**：平滑的状态变化，避免突兀的跳转
- **加载体验**：优雅的加载状态和骨架屏设计

### 📐 信息架构 (Information Architecture)
- **层次清晰**：主要信息 → 次要信息 → 辅助信息
- **扫视模式**：支持用户快速扫视获取关键信息
- **认知负荷**：减少用户思考成本，直观易懂
- **一致性**：相同类型信息使用统一的呈现方式

### 🎨 色彩克制 (Restrained Color Palette)
- **有限色彩**：主色调 + 1-2个辅助色 + 中性色系
- **品牌一致**：严格遵循品牌色彩规范
- **语义化色彩**：成功(绿)、警告(橙)、错误(红)、信息(蓝)
- **渐变运用**：适度使用渐变营造现代感，避免过度炫技

## 🚀 布局规范

### Flex布局优先
```html
<!-- 推荐的布局模式 -->
<view class="flex-x-between items-center p-4">
  <view class="flex-x items-center gap-3">
    <image class="avatar" />
    <text class="name">用户名</text>
  </view>
  <button class="action-btn">操作</button>
</view>
```

**预定义工具类：**
- `flex-x`: 水平排列
- `flex-y`: 垂直排列  
- `flex-x-center`: 水平排列并垂直居中
- `flex-x-between`: 水平排列、两端对齐、垂直居中
- `flex-y-center`: 垂直排列并水平居中

### 间距系统 (8pt Grid)
```scss
// 推荐间距（基于8rpx倍数）
$spacing-xs: 8rpx;   // 超小间距
$spacing-sm: 16rpx;  // 小间距  
$spacing-md: 24rpx;  // 中等间距
$spacing-lg: 32rpx;  // 大间距
$spacing-xl: 48rpx;  // 超大间距
```

### 圆角规范
```scss
// 现代化圆角系统
$radius-xs: 8rpx;    // 小元素圆角
$radius-sm: 12rpx;   // 按钮、标签圆角
$radius-md: 16rpx;   // 卡片圆角
$radius-lg: 20rpx;   // 大卡片圆角
$radius-xl: 24rpx;   // 容器圆角
$radius-full: 50%;   // 圆形元素
```

## 🎭 组件设计原则

### 卡片组件
```scss
.modern-card {
  background: linear-gradient(135deg, #ffffff, #fafbfc);
  border-radius: $radius-lg;
  padding: $spacing-lg;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f5f5f5;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  
  &:active {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
  }
}
```

### 按钮组件
```scss
.primary-btn {
  background: linear-gradient(135deg, var(--primary), var(--primary-400));
  color: #ffffff;
  border-radius: $radius-md;
  padding: $spacing-sm $spacing-lg;
  font-weight: 600;
  transition: all 0.2s ease;
  
  &:active {
    transform: scale(0.95);
    opacity: 0.9;
  }
}
```

### 输入框组件
```scss
.modern-input {
  background: #f8f9fa;
  border: 2rpx solid transparent;
  border-radius: $radius-md;
  padding: $spacing-sm $spacing-md;
  transition: all 0.2s ease;
  
  &:focus {
    background: #ffffff;
    border-color: var(--primary);
    box-shadow: 0 0 0 6rpx rgba(var(--primary-rgb), 0.1);
  }
}
```

## 🎯 字体规范

### 字体层次
```scss
// 现代字体系统
$font-xs: 20rpx;    // 辅助文字
$font-sm: 24rpx;    // 次要文字  
$font-base: 28rpx;  // 正文文字
$font-lg: 32rpx;    // 标题文字
$font-xl: 36rpx;    // 大标题
$font-2xl: 42rpx;   // 页面标题

// 字重系统
$font-normal: 400;
$font-medium: 500;
$font-semibold: 600;
$font-bold: 700;
```

## 🌈 色彩系统

### 主色调
```scss
:root {
  --primary: #6366f1;
  --primary-50: #eef2ff;
  --primary-100: #e0e7ff;
  --primary-400: #818cf8;
  --primary-600: #4f46e5;
  
  // 语义化色彩
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;
  
  // 中性色
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-500: #6b7280;
  --gray-900: #111827;
}
```

## ⚡ 动画与过渡

### 标准缓动函数
```scss
// 推荐的缓动函数
$ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
$ease-out-expo: cubic-bezier(0.19, 1, 0.22, 1);
$ease-in-out-back: cubic-bezier(0.68, -0.55, 0.265, 1.55);

// 标准动画时长
$duration-fast: 0.15s;
$duration-normal: 0.25s;
$duration-slow: 0.35s;
```

### 常用动画模式
```scss
// 淡入动画
.fade-in {
  animation: fadeIn $duration-normal $ease-out-quart;
}

// 滑入动画  
.slide-up {
  animation: slideUp $duration-normal $ease-out-expo;
}

// 弹性按钮
.bounce-btn:active {
  transform: scale(0.95);
  transition: transform $duration-fast $ease-in-out-back;
}
```

## 📝 代码规范

### SCSS最佳实践
```scss
// ✅ 推荐写法
.user-card {
  @include modern-card;
  
  .user-info {
    display: flex;
    align-items: center;
    gap: $spacing-md;
    
    .avatar {
      @include circle(80rpx);
      background: var(--primary-100);
    }
    
    .user-name {
      font-size: $font-lg;
      font-weight: $font-semibold;
      color: var(--gray-900);
    }
  }
  
  .action-section {
    margin-top: $spacing-lg;
    
    .primary-action {
      @include primary-btn;
    }
  }
}
```

### 组件命名规范
- **BEM方法论**：Block__Element--Modifier
- **语义化命名**：描述功能而非样式
- **一致性前缀**：相同类型组件使用统一前缀

## 🎪 实战示例

### 人才卡片组件 (参考实现)
```scss
.talent-card {
  @include modern-card;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: $spacing-lg;
    
    .user-section {
      display: flex;
      gap: $spacing-md;
      
      .avatar {
        @include circle(80rpx);
        background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
        
        .avatar-text {
          color: var(--primary);
          font-weight: $font-semibold;
        }
      }
    }
  }
  
  .skills-section {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-sm;
    
    .skill-tag {
      background: var(--gray-100);
      color: var(--gray-600);
      padding: $spacing-xs $spacing-sm;
      border-radius: $radius-sm;
      font-size: $font-xs;
      font-weight: $font-medium;
    }
  }
}
```

---

💡 **记住**: 每一个像素都有意义，每一个交互都应当自然流畅。我们追求的不仅是功能实现，更是用户体验的艺术。
