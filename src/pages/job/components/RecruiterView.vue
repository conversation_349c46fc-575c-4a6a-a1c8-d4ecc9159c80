<template>
  <view>
    <!-- 人才筛选 -->
    <view class="talent-filter px-20rpx mb-20rpx">
      <view class="filter-header flex justify-between items-center mb-20rpx">
        <text class="text-30rpx font-bold">人才筛选</text>
        <view
          class="filter-more flex items-center"
          @click="openTalentFilterPopup"
        >
          <text class="text-secondary">筛选</text>
          <text class="i-carbon-filter ml-4rpx text-secondary"></text>
        </view>
      </view>
      <view class="talent-filter-tabs flex">
        <view
          v-for="(tab, index) in talentFilterTabs"
          :key="index"
          class="talent-filter-tab"
          :class="{ 'active-tab': tab.active }"
          @click="handleTalentFilterTabClick(index)"
        >
          <text class="text-28rpx">{{ tab.name }}</text>
        </view>
      </view>
    </view>

    <!-- 人才列表 -->
    <view class="talent-list flex-y gap-20rpx mx-20rpx" v-if="!isLoading">
      <template v-if="talentList.length > 0">
        <TalentItem
          v-for="(talent, index) in talentList"
          :key="index"
          :talent="talent"
          @click="goToTalentDetail(talent)"
          @contact="contactTalent(talent)"
        />
      </template>
      <tui-no-data
        v-else
        imgUrl="/static/images/no-data.png"
        tips="暂无相关人才"
      />
    </view>
    <tui-loading v-else />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import TalentItem from "@/components/job/TalentItem.vue";

const emit = defineEmits(["openTalentFilterPopup"]);

// 人才筛选相关
const talentFilterTabs = ref([
  { name: "全部", active: true },
  { name: "已投递", active: false },
  { name: "待面试", active: false },
  { name: "已拒绝", active: false },
]);

const talentList = ref([]);
const isLoading = ref(false);

// 初始化数据
const initData = async () => {
  try {
    isLoading.value = true;
    // 模拟数据加载
    await new Promise((resolve) => setTimeout(resolve, 1000));
    talentList.value = [
      {
        id: 1,
        name: "张三",
        gender: "男",
        jobTitle: "前端开发工程师",
        experience: "3年",
        education: "本科",
        location: "广州",
        lastActivity: "刚刚",
        skills: ["Vue", "React", "TypeScript"],
        status: "active",
      },
      // ... 其他数据
    ];
  } catch (error) {
    console.error("加载人才列表失败:", error);
    uni.showToast({
      title: "加载失败，请重试",
      icon: "none",
    });
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => {
  initData();
});

const handleTalentFilterTabClick = (index: number) => {
  talentFilterTabs.value.forEach((tab, idx) => {
    tab.active = idx === index;
  });
  // 根据筛选条件获取数据
};

const openTalentFilterPopup = () => {
  emit("openTalentFilterPopup");
};

// 跳转到人才详情页
const goToTalentDetail = (talent: any) => {
  uni.navigateTo({
    url: `/pages/job/recruiter/talent-detail?id=${talent.id}`,
  });
};

// 联系人才
const contactTalent = (talent: any) => {
  uni.showModal({
    title: "联系人才",
    content: `确定要联系 ${talent.name} 吗？`,
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: "联系请求已发送",
          icon: "success",
        });
      }
    },
  });
};
</script>

<style lang="scss" scoped>
// 人才筛选
.talent-filter {
  background-color: $bg-card;
  border-radius: $radius-lg;
  padding: $spacing-20;

  .talent-filter-tabs {
    display: flex;
    gap: $spacing-16;
    margin-top: $spacing-12;

    .talent-filter-tab {
      padding: $spacing-12 $spacing-16;
      background-color: $bg-tag;
      border-radius: $radius-lg;
      color: $text-secondary;
      font-size: $font-size-base;
      transition: all 0.2s;
      border: 1rpx solid $border-color;

      &.active-tab {
        background-color: $primary;
        color: $text-inverse;
        border-color: $primary;
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }

  .filter-more {
    padding: $spacing-8 $spacing-12;
    background-color: $bg-tag;
    border-radius: $radius;
    color: $text-secondary;
    font-size: $font-size-sm;
    transition: all 0.2s;

    &:active {
      background-color: $bg-search;
      transform: scale(0.98);
    }
  }
}
</style>
