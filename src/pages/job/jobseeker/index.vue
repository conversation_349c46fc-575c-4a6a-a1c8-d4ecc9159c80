<template>
  <view class="jobseeker-container">
    <uni-nav-bar
      fixed
      statusBar="true"
      :border="false"
      title=""
      left-icon="back"
      :backgroundColor="scrollTop > 50 ? '#ffffff' : 'transparent'"
      @clickLeft="goBack"
    />

    <!-- 搜索框 -->
    <view class="search-section">
      <view class="search-box">
        <view class="search-input-box">
          <text class="i-solar:minimalistic-magnifer-linear search-icon"></text>
          <input
            class="search-input"
            type="text"
            placeholder="搜索职位、公司、技能"
            v-model="searchKeyword"
            @confirm="handleSearch"
          />
          <view class="search-btn" @tap="handleSearch">搜索</view>
        </view>
      </view>
    </view>

    <!-- 工具栏 -->
    <ToolGrid
      :tools="jobseekerTools"
      role="jobseeker"
      @tool-click="handleToolClick"
    />

    <!-- 意向职位 -->
    <view class="intention-jobs">
      <view class="section-header">
        <text class="section-title">意向职位</text>
        <view class="section-actions">
          <text class="more-text" @tap="showMoreJobs">更多</text>
          <view class="add-btn" @tap="navigateToEditIntention">
            <text class="i-carbon-add"></text>
          </view>
        </view>
      </view>

      <scroll-view scroll-x class="intention-scroll" show-scrollbar="false">
        <view class="intention-list">
          <view
            v-for="(job, index) in jobStore.intentionJobs"
            :key="job.id"
            class="intention-item"
            :class="{ active: job.active }"
            @tap="selectIntentionJob(job.id)"
          >
            {{ job.name }}
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 筛选栏 -->
    <view class="filter-section">
      <view class="filter-tabs">
        <view
          v-for="(tab, index) in filterTabs"
          :key="index"
          class="filter-tab"
          :class="{ active: tab.active }"
          @click="handleFilterTabClick(index)"
        >
          <text>{{ tab.name }}</text>
        </view>
      </view>

      <view class="filter-btn" @click="openFilterPopup">
        <text class="filter-text">筛选</text>
        <view v-if="jobStore.filterCount > 0" class="filter-count">
          {{ jobStore.filterCount }}
        </view>
        <text class="i-carbon-filter filter-icon"></text>
      </view>
    </view>

    <!-- 职位列表 -->
    <view class="job-list">
      <JobItem
        v-for="(job, index) in jobList"
        :key="job.id"
        :job="job"
        @click="goToJobDetail(job)"
      />
    </view>

    <!-- 筛选弹窗 -->
    <JobFilterPopup
      v-model="showFilterPopup"
      @confirm="handleFilterConfirm"
      @reset="handleFilterReset"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useJobStore } from "@/stores/job";
import ToolGrid from "../components/ToolGrid.vue";
import JobItem from "@/components/job/JobItem.vue";
import JobFilterPopup from "../components/JobFilterPopup.vue";

const jobStore = useJobStore();
const scrollTop = ref(0);
const searchKeyword = ref("");
const showFilterPopup = ref(false);

// 页面加载时恢复角色状态
onMounted(() => {
  jobStore.restoreRole();
});

// 求职者工具
const jobseekerTools = [
  {
    id: "nearby",
    name: "附近",
    icon: "i-carbon-location",
    bgColor: "bg-primary",
  },
  {
    id: "part-time",
    name: "兼职",
    icon: "i-carbon-time",
    bgColor: "bg-success",
  },
  { id: "resume", name: "简历", icon: "i-carbon-document", bgColor: "bg-info" },
  {
    id: "intention",
    name: "意向",
    icon: "i-carbon-favorite",
    bgColor: "bg-warning",
  },
  { id: "all-jobs", name: "全部", icon: "i-carbon-grid", bgColor: "bg-purple" },
];

// 筛选标签
const filterTabs = ref([
  { name: "推荐", active: true },
  { name: "最新", active: false },
  { name: "附近", active: false },
  { name: "高薪", active: false },
]);

// 模拟职位数据
const jobList = ref([
  {
    id: "job1001",
    title: "前端开发工程师",
    isUrgent: true,
    salary: "15-25K·14薪",
    area: "朝阳区",
    tags: ["3-5年", "本科", "Vue", "React", "弹性工作"],
    companyName: "字节跳动",
    industry: "互联网",
    publishTime: "今天",
  },
  {
    id: "job1002",
    title: "产品经理",
    isUrgent: false,
    salary: "20-30K·13薪",
    area: "海淀区",
    tags: ["3-5年", "本科", "产品设计", "需求分析"],
    companyName: "腾讯",
    industry: "互联网",
    publishTime: "昨天",
  },
]);

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 处理搜索
const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    uni.showToast({ title: "请输入搜索关键词", icon: "none" });
    return;
  }
  uni.navigateTo({
    url: `/pages/job/jobseeker/search?keyword=${encodeURIComponent(
      searchKeyword.value
    )}`,
  });
};

// 处理工具点击
const handleToolClick = (tool: any) => {
  const routes = {
    nearby: "/pages/job/jobseeker/nearby",
    "part-time": "/pages/job/jobseeker/part-time",
    resume: "/pages/job/resume",
    intention: "/pages/job/intention-edit",
    "all-jobs": "/pages/job/jobseeker/all",
  };

  if (routes[tool.id]) {
    uni.navigateTo({ url: routes[tool.id] });
  }
};

// 选择意向职位
const selectIntentionJob = (id: string) => {
  jobStore.selectIntentionJob(id);
};

// 查看更多职位
const showMoreJobs = () => {
  uni.navigateTo({ url: "/pages/job/jobseeker/categories" });
};

// 导航到意向编辑
const navigateToEditIntention = () => {
  uni.navigateTo({ url: "/pages/job/intention-edit" });
};

// 处理筛选标签点击
const handleFilterTabClick = (index: number) => {
  filterTabs.value.forEach((tab, idx) => {
    tab.active = idx === index;
  });
};

// 打开筛选弹窗
const openFilterPopup = () => {
  showFilterPopup.value = true;
};

// 处理筛选确认
const handleFilterConfirm = (filters: any) => {
  console.log("应用筛选条件:", filters);
  // 根据筛选条件重新获取数据
};

// 处理筛选重置
const handleFilterReset = () => {
  jobStore.resetFilters();
};

// 跳转到职位详情
const goToJobDetail = (job: any) => {
  uni.navigateTo({
    url: `/pages/job/detail?id=${job.id}`,
  });
};
</script>

<style lang="scss" scoped>
.jobseeker-container {
  background: linear-gradient(
    to bottom,
    #e3eeff 0%,
    #eef3ff 10%,
    var(--bg-page) 20%
  );
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.search-section {
  padding: 20rpx;
  margin-top: 20rpx;

  .search-box {
    .search-input-box {
      display: flex;
      align-items: center;
      background-color: #fff;
      border-radius: 36rpx;
      padding: 0 8rpx 0 24rpx;
      height: 80rpx;
      border: 2rpx solid var(--primary-400);
      box-shadow: 0 4rpx 12rpx rgba(var(--primary-rgb), 0.1);

      .search-icon {
        font-size: 32rpx;
        color: var(--text-info);
        margin-right: 10rpx;
      }

      .search-input {
        flex: 1;
        height: 80rpx;
        font-size: 28rpx;
        color: #333;
      }

      .search-btn {
        width: 120rpx;
        height: 64rpx;
        background: linear-gradient(90deg, var(--primary-400), var(--primary));
        color: #fff;
        text-align: center;
        line-height: 64rpx;
        border-radius: 32rpx;
        margin-left: 10rpx;
        font-size: 28rpx;
        box-shadow: 0 4rpx 8rpx rgba(var(--primary-rgb), 0.3);
        transition: all 0.2s;

        &:active {
          transform: scale(0.95);
        }
      }
    }
  }
}

.intention-jobs {
  background-color: #fff;
  border-radius: 20rpx;
  margin: 20rpx;
  padding: 24rpx;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: var(--text-base);
    }

    .section-actions {
      display: flex;
      align-items: center;
      gap: 16rpx;

      .more-text {
        font-size: 26rpx;
        color: var(--text-secondary);
      }

      .add-btn {
        width: 48rpx;
        height: 48rpx;
        border-radius: 24rpx;
        background-color: var(--primary);
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4rpx 8rpx rgba(var(--primary-rgb), 0.3);
      }
    }
  }

  .intention-scroll {
    white-space: nowrap;

    .intention-list {
      display: flex;
      gap: 16rpx;
      padding: 10rpx 0;

      .intention-item {
        padding: 16rpx 24rpx;
        background-color: var(--bg-secondary);
        color: var(--text-secondary);
        font-size: 28rpx;
        border-radius: 24rpx;
        white-space: nowrap;
        transition: all 0.2s;

        &.active {
          background-color: var(--primary);
          color: #fff;
        }

        &:active {
          transform: scale(0.95);
        }
      }
    }
  }
}

.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #fff;
  margin: 0 20rpx 20rpx;
  border-radius: 20rpx;

  .filter-tabs {
    display: flex;
    gap: 32rpx;

    .filter-tab {
      position: relative;
      padding: 16rpx 8rpx;
      font-size: 30rpx;
      color: var(--text-secondary);
      transition: all 0.2s;

      &.active {
        color: var(--primary);
        font-weight: 600;

        &::after {
          content: "";
          position: absolute;
          bottom: 8rpx;
          left: 50%;
          transform: translateX(-50%);
          width: 24rpx;
          height: 4rpx;
          background-color: var(--primary);
          border-radius: 2rpx;
        }
      }
    }
  }

  .filter-btn {
    display: flex;
    align-items: center;
    gap: 8rpx;
    padding: 12rpx 16rpx;
    background-color: var(--bg-secondary);
    border-radius: 20rpx;
    position: relative;
    transition: all 0.2s;

    &:active {
      transform: scale(0.95);
    }

    .filter-text {
      font-size: 26rpx;
      color: var(--text-secondary);
    }

    .filter-count {
      position: absolute;
      top: -8rpx;
      right: -8rpx;
      width: 32rpx;
      height: 32rpx;
      background-color: var(--error);
      color: #fff;
      font-size: 20rpx;
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .filter-icon {
      font-size: 24rpx;
      color: var(--text-secondary);
    }
  }
}

.job-list {
  padding: 0 20rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
</style>
