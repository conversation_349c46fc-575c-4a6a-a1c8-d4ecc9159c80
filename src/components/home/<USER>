<template>
  <view class="container pb-100rpx">
    <!-- 广告轮播图 -->
    <view v-if="showBanner" class="ad-banner-container px-20rpx">
      <swiper
        class="ad-banner-swiper"
        :indicator-dots="true"
        :autoplay="true"
        :interval="3000"
        :duration="500"
        indicator-color="rgba(255, 255, 255, 0.6)"
        indicator-active-color="#ff6d00"
      >
        <swiper-item v-for="(item, index) in adBannerList" :key="index">
          <image :src="item.image" mode="aspectFill" class="ad-banner-image" />
        </swiper-item>
      </swiper>
    </view>

    <!-- 重要功能入口 -->
    <view class="job-tools bg-card px-20rpx py-30rpx mb-20rpx">
      <view class="grid grid-cols-5 gap-20rpx">
        <view
          v-for="(tool, index) in jobTools"
          :key="index"
          class="job-tool-item flex flex-col items-center"
          @tap="handleToolClick(tool.id)"
        >
          <view class="tool-icon-circle mb-10rpx" :class="tool.bgColor">
            <text :class="tool.icon"></text>
          </view>
          <text class="font-sm text-info">{{ tool.name }}</text>
        </view>
      </view>
    </view>

    <!-- 职位筛选栏 -->
    <view
      class="filter-bar flex items-center justify-between bg-card px-32rpx py-20rpx mb-20rpx"
    >
      <view class="filter-tabs flex">
        <view
          v-for="(tab, index) in filterTabs"
          :key="index"
          class="filter-tab mr-30rpx"
          :class="{ 'active-tab': tab.active }"
          @click="handleFilterTabClick(index)"
        >
          <text class="font-base">{{ tab.name }}</text>
        </view>
      </view>
      <view class="filter-more flex items-center" @click="openFilterPopup">
        <text class="font-base text-info">筛选</text>
        <text class="i-carbon-filter ml-4rpx text-info"></text>
      </view>
    </view>

    <!-- 推荐职位 -->
    <view class="recommend-jobs">
      <!-- 职位列表 -->
      <view class="job-list mx-20rpx">
        <view
          v-for="(job, index) in recommendJobs"
          :key="index"
          class="job-item p-24rpx my-16rpx bg-card rounded-lg shadow"
          @tap="goToJobDetail(job)"
        >
          <!-- 职位头部信息，包含职位名称和薪资 -->
          <view class="flex justify-between items-start mb-16rpx">
            <view class="flex-1 mr-20rpx">
              <view class="job-title-row flex items-center">
                <text
                  class="job-title font-xl font-bold text-base text-line-2"
                  >{{ job.title }}</text
                >
                <view
                  v-if="job.isUrgent"
                  class="tag tag-danger-outlined ml-10rpx"
                  >急聘</view
                >
              </view>

              <!-- 公司信息 -->
              <view class="company-info flex items-center mt-12rpx">
                <text class="company-name font-base font-medium text-info">{{
                  job.companyName
                }}</text>
                <text class="text-grey font-sm mx-12rpx">|</text>
                <text class="text-grey font-sm">{{ job.industry }}</text>
              </view>

              <!-- 职位标签 -->
              <view class="tag-container mt-12rpx">
                <view
                  v-for="(tag, tagIndex) in job.tags.slice(0, 3)"
                  :key="tagIndex"
                  class="tag"
                >
                  {{ tag }}
                </view>
              </view>
            </view>

            <view class="salary-container">
              <view class="salary font-lg font-bold">{{ job.salary }}</view>
            </view>
          </view>

          <!-- 底部位置和时间信息 -->
          <view class="divider mb-16rpx"></view>

          <view class="flex justify-between items-center">
            <view class="flex items-center">
              <text class="i-carbon-location text-grey font-xs mr-6rpx"></text>
              <text class="area text-grey font-sm">{{ job.area }}</text>
            </view>
            <text class="text-grey font-xs">{{ job.publishTime }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 热门企业 -->
    <view class="hot-companies bg-card">
      <view
        class="section-header flex justify-between items-center px-20rpx py-20rpx"
      >
        <text class="font-lg font-bold">热门企业</text>
        <view class="flex items-center">
          <text class="font-sm text-grey">查看全部</text>
          <text class="i-carbon-chevron-right text-grey"></text>
        </view>
      </view>

      <scroll-view scroll-x class="companies-scroll">
        <view class="flex px-20rpx pb-20rpx">
          <view
            v-for="(company, index) in hotCompanies"
            :key="index"
            class="company-item mr-20rpx rounded-lg overflow-hidden shadow"
          >
            <image
              :src="company.image"
              mode="aspectFill"
              class="company-image"
            />
            <view class="company-info p-15rpx">
              <view class="flex items-center justify-between">
                <text class="company-name font-base font-bold">{{
                  company.name
                }}</text>
                <text class="jobs-count font-xs text-primary"
                  >{{ company.jobsCount }}个职位</text
                >
              </view>
              <view class="flex items-center mt-5rpx">
                <text class="text-grey font-xs">{{ company.industry }}</text>
                <text class="text-grey font-xs ml-10rpx">{{
                  company.size
                }}</text>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 筛选弹出层 -->
    <tui-bottom-popup
      :show="showFilterPopup"
      :height="1000"
      :isSafeArea="true"
      :zIndex="1002"
      :maskZIndex="1001"
      backgroundColor="#ffffff"
      @close="showFilterPopup = false"
    >
      <view class="filter-popup-content">
        <view
          class="filter-popup-header flex justify-between items-center px-30rpx py-20rpx"
        >
          <text class="font-lg font-bold">筛选</text>
          <text
            class="close-btn i-carbon-close"
            @tap="showFilterPopup = false"
          ></text>
        </view>

        <!-- 筛选选项 -->
        <scroll-view scroll-y enable-flex class="filter-options-scroll">
          <filter-option
            title="经验要求"
            type="experience"
            :options="experienceOptions"
            @option-change="handleOptionChange"
          />

          <filter-option
            title="职位类型"
            type="jobType"
            :options="jobTypeOptions"
            @option-change="handleOptionChange"
          />

          <filter-option
            title="学历要求"
            type="education"
            :options="educationOptions"
            @option-change="handleOptionChange"
          />

          <filter-option
            title="薪资待遇"
            type="salary"
            :options="salaryOptions"
            @option-change="handleOptionChange"
          />

          <filter-option
            title="公司规模"
            type="companySize"
            :options="companySizeOptions"
            @option-change="handleOptionChange"
          />

          <filter-option
            title="福利待遇"
            type="welfare"
            :options="welfareOptions"
            :isLastSection="true"
            @option-change="handleOptionChange"
          />
        </scroll-view>

        <!-- 操作按钮 -->
        <view class="filter-actions flex px-30rpx py-20rpx">
          <view
            class="reset-btn flex-1 text-center py-20rpx mr-20rpx"
            @tap="resetFilter"
            >清除</view
          >
          <view
            class="confirm-btn flex-1 text-center py-20rpx"
            @tap="confirmFilter"
            >确定</view
          >
        </view>
      </view>
    </tui-bottom-popup>
  </view>
</template>

<script setup lang="ts">
import { defineProps, ref } from "vue";
import tuiBottomPopup from "@/components/thorui/tui-bottom-popup/tui-bottom-popup.vue";
import FilterOption from "@/components/home/<USER>";

// 定义props接收是否显示广告banner
const props = defineProps({
  showBanner: {
    type: Boolean,
    default: true,
  },
});

// 广告轮播图数据
const adBannerList = [
  {
    image: "https://picsum.photos/seed/job-ad1/700/238",
    link: "https://example.com/job/ad1",
  },
  {
    image: "https://picsum.photos/seed/job-ad2/700/238",
    link: "https://example.com/job/ad2",
  },
  {
    image: "https://picsum.photos/seed/job-ad3/700/238",
    link: "https://example.com/job/ad3",
  },
];

// 实用功能入口
const jobTools = [
  {
    id: "resume",
    name: "在线简历",
    icon: "i-carbon-document",
    bgColor: "bg-blue-500",
  },
  {
    id: "company-auth",
    name: "企业认证",
    icon: "i-carbon-certificate",
    bgColor: "bg-orange-500",
  },
  {
    id: "publish",
    name: "发布招聘",
    icon: "i-carbon-add-alt",
    bgColor: "bg-green-500",
  },
  {
    id: "manage",
    name: "职位管理",
    icon: "i-carbon-table",
    bgColor: "bg-purple-500",
  },
  {
    id: "job-fair",
    name: "线下招聘会",
    icon: "i-carbon-event",
    bgColor: "bg-cyan-500",
  },
];

// 筛选标签
const filterTabs = [
  { name: "推荐", active: true },
  { name: "最新", active: false },
  { name: "附近", active: false },
];

// 推荐职位
const recommendJobs = [
  {
    id: "job1001",
    title: "前端开发工程师",
    isUrgent: true,
    salary: "15-25K·13薪",
    area: "海淀区",
    companyLogo: "https://picsum.photos/seed/company1/80/80",
    tags: ["3-5年", "本科", "前端开发", "Vue", "React"],
    companyName: "字节跳动",
    industry: "互联网",
    publishTime: "1小时前",
  },
  {
    id: "job1002",
    title: "销售经理",
    isUrgent: false,
    salary: "10-15K·底薪+提成",
    area: "朝阳区",
    companyLogo: "https://picsum.photos/seed/company2/80/80",
    tags: ["1-3年", "大专", "销售", "市场拓展"],
    companyName: "新东方教育",
    industry: "教育培训",
    publishTime: "3小时前",
  },
  {
    id: "job1003",
    title: "Java开发工程师",
    isUrgent: true,
    salary: "20-35K·14薪",
    area: "中关村",
    companyLogo: "https://picsum.photos/seed/company3/80/80",
    tags: ["5-10年", "本科", "Java", "微服务", "Spring"],
    companyName: "阿里巴巴",
    industry: "互联网",
    publishTime: "昨天",
  },
];

// 热门企业
const hotCompanies = [
  {
    name: "腾讯科技",
    image: "https://picsum.photos/seed/tencent/350/150",
    jobsCount: "238",
    industry: "互联网",
    size: "10000人以上",
  },
  {
    name: "百度",
    image: "https://picsum.photos/seed/baidu/350/150",
    jobsCount: "156",
    industry: "互联网",
    size: "10000人以上",
  },
  {
    name: "京东",
    image: "https://picsum.photos/seed/jd/350/150",
    jobsCount: "189",
    industry: "互联网/电商",
    size: "10000人以上",
  },
];

// 弹出层控制
const showFilterPopup = ref(false);

// 筛选选项
const experienceOptions = [
  { name: "全部", active: true },
  { name: "1年以内", active: false },
  { name: "1-3年", active: false },
  { name: "3-5年", active: false },
  { name: "5-10年", active: false },
  { name: "10年以上", active: false },
  { name: "应届生", active: false },
  { name: "在校生", active: false },
];

// 职位类型选项
const jobTypeOptions = [
  { name: "全部", active: true },
  { name: "全职", active: false },
  { name: "兼职", active: false },
  { name: "实习", active: false },
  { name: "临时工", active: false },
  { name: "自由职业", active: false },
  { name: "校招", active: false },
  { name: "社招", active: false },
];

// 学历要求选项
const educationOptions = [
  { name: "全部", active: true },
  { name: "不限", active: false },
  { name: "中专", active: false },
  { name: "大专", active: false },
  { name: "本科", active: false },
  { name: "硕士", active: false },
  { name: "博士", active: false },
];

// 福利待遇选项
const welfareOptions = [
  { name: "五险一金", active: false },
  { name: "包吃", active: false },
  { name: "包住", active: false },
  { name: "年终奖", active: false },
  { name: "加班费", active: false },
  { name: "定期体检", active: false },
  { name: "带薪年假", active: false },
  { name: "交通补贴", active: false },
  { name: "餐补", active: false },
  { name: "通讯补贴", active: false },
  { name: "医疗保险", active: false },
  { name: "社保", active: false },
];

const salaryOptions = [
  { name: "全部", active: true },
  { name: "15K以下", active: false },
  { name: "15-25K", active: false },
  { name: "25-35K", active: false },
  { name: "35-45K", active: false },
  { name: "45K以上", active: false },
];

const companySizeOptions = [
  { name: "全部", active: true },
  { name: "0-20人", active: false },
  { name: "20-99人", active: false },
  { name: "100-499人", active: false },
  { name: "500-999人", active: false },
  { name: "1000-9999人", active: false },
  { name: "10000人以上", active: false },
];

// 处理工具点击
const handleToolClick = (toolId) => {
  console.log("clicked tool:", toolId);
  // TODO: 根据工具ID跳转到相应页面
  uni.navigateTo({
    url: `/pages/job/${toolId}`,
  });
};

// 处理筛选标签点击
const handleFilterTabClick = (index) => {
  filterTabs.forEach((tab, idx) => {
    tab.active = idx === index;
  });
  // TODO: 根据筛选条件获取数据
};

// 切换筛选选项
const toggleFilterOption = (type, index) => {
  let options;

  if (type === "experience") {
    options = experienceOptions;
  } else if (type === "jobType") {
    options = jobTypeOptions;
  } else if (type === "education") {
    options = educationOptions;
  } else if (type === "salary") {
    options = salaryOptions;
  } else if (type === "companySize") {
    options = companySizeOptions;
  } else if (type === "welfare") {
    options = welfareOptions;
    // 福利待遇是多选，直接切换状态并返回
    options[index].active = !options[index].active;
    return;
  }

  if (index === 0) {
    // 如果是"全部"选项
    options.forEach((opt, idx) => {
      opt.active = idx === 0;
    });
  } else {
    options[0].active = false; // 取消"全部"的选中状态
    options[index].active = !options[index].active;

    // 如果没有选中任何选项，则自动选中"全部"
    if (!options.some((opt) => opt.active)) {
      options[0].active = true;
    }
  }
};

// 重置筛选
const resetFilter = () => {
  experienceOptions.forEach((opt, idx) => {
    opt.active = idx === 0;
  });

  jobTypeOptions.forEach((opt, idx) => {
    opt.active = idx === 0;
  });

  educationOptions.forEach((opt, idx) => {
    opt.active = idx === 0;
  });

  salaryOptions.forEach((opt, idx) => {
    opt.active = idx === 0;
  });

  companySizeOptions.forEach((opt, idx) => {
    opt.active = idx === 0;
  });

  // 福利待遇全部重置为未选中
  welfareOptions.forEach((opt) => {
    opt.active = false;
  });
};

// 打开筛选弹出层
const openFilterPopup = () => {
  showFilterPopup.value = true;
};

// 处理筛选选项变更
const handleOptionChange = ({ type, index }) => {
  toggleFilterOption(type, index);
};

// 确认筛选
const confirmFilter = () => {
  showFilterPopup.value = false;
  // TODO: 根据筛选条件获取数据
};

// 跳转到职位详情页
const goToJobDetail = (job) => {
  // 可以传递职位ID或完整职位数据
  uni.navigateTo({
    url: "/pages/job/detail?id=" + job.id,
  });
};
</script>

<style lang="scss" scoped>
.ad-banner-container {
  margin-bottom: 20rpx;
}

.ad-banner-swiper {
  height: 238rpx;
  border-radius: $radius;
  overflow: hidden;
}

.ad-banner-image {
  width: 100%;
  height: 238rpx;
}

.tool-icon-circle {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 40rpx;
}

.filter-bar {
  border-bottom: 1rpx solid $border-color;
}

.filter-tab {
  position: relative;
  padding-bottom: 10rpx;
}

.active-tab {
  color: $primary;
  font-weight: $font-weight-bold;
}

.active-tab:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30rpx;
  height: 6rpx;
  background-color: $primary;
  border-radius: 3rpx;
}

.salary-container {
  padding: 8rpx 16rpx;
  border-radius: $radius-sm;
  margin-left: 10rpx;

  .salary {
    color: $primary;
  }
}

.companies-scroll {
  white-space: nowrap;
}

.company-item {
  width: 350rpx;
  display: inline-block;
  background-color: $bg-card;
}

.company-image {
  width: 350rpx;
  height: 150rpx;
}

/* 筛选弹出层样式 */
.filter-popup-content {
  background-color: $bg-card;
  height: 100%;
  display: flex;
  flex-direction: column;
  height: 75vh;
  padding-bottom: 100rpx;
}

.filter-popup-header {
  border-bottom: 1rpx solid $border-color;
}

.close-btn {
  font-size: 40rpx;
  color: $text-grey;
}

.filter-options-scroll {
  flex: 1;
  height: calc(100% - 160rpx);
}

.filter-actions {
  padding: 30rpx;
  border-top: 1rpx solid $border-color;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: $bg-card;
  z-index: 9;
}

.reset-btn {
  border: 1rpx solid $border-color;
  border-radius: $radius-sm;
  font-size: $font-size-base;
  color: $text-secondary;
  transition: all 0.2s;
}

.confirm-btn {
  background-color: $primary;
  color: $text-inverse;
  border-radius: $radius-sm;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  transition: all 0.2s;
}

.confirm-btn:active {
  opacity: 0.8;
}
</style>
