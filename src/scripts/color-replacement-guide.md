# 颜色替换指南

本文档提供了项目中颜色系统的更新说明以及如何在项目代码中正确使用新的颜色变量。

## 全局颜色变量

项目的全局颜色定义在 `src/uni.scss` 文件中，主要包括以下几类：

### 主题色

- `$primary`: 主题色，橙色系 (#ff6d00)
- `$primary-50` 到 `$primary-950`: 主题色的不同深浅变体

### 文本颜色

- `$text-base`: 主要文本颜色 (#212529)，替代原来的 #333/#333333
- `$text-secondary`: 次要文本颜色 (#52525b)，替代原来的 #666/#666666
- `$text-info`: 描述文本颜色 (#8f9098)，用于标签和描述性文字
- `$text-grey`: 占位文字颜色 (#a1a1aa)，替代原来的 #999/#999999
- `$text-disable`: 禁用状态文字颜色 (#d4d4d8)
- `$text-inverse`: 反色文字，通常为白色 (#feffff)
- `$text-red`: 红色文本，用于警告和错误 (#dc2626)

### 背景颜色

- `$bg-page`: 页面背景色 (#f4f5fa)，替代原来的 #f5f5f5
- `$bg-tag`: 标签背景色 (#f2f2f7)
- `$bg-card`: 卡片背景色 (#feffff)，替代纯白色背景
- `$bg-search`: 搜索框背景色 (#f1f2f7)
- `$bg-mask`: 遮罩背景色 (rgba(0, 0, 0, 0.5))

### 其他

- `$border-color`: 边框颜色 (#e4e4e7)，替代原来的 #f0f0f0 等

## CSS 类名

在项目中，我们使用了多种方式来应用颜色：

### UnoCSS 颜色类名

在 `uno.config.ts` 中定义了以下颜色相关的类名：

- 文本颜色：
  - `text-base`: 主文本颜色
  - `text-secondary`: 次要文本颜色
  - `text-info`: 描述文本颜色
  - `text-grey`: 占位文本颜色
  - `text-disable`: 禁用文本颜色
  - `text-inverse`: 反色文本
  - `text-primary`: 主题色文本

- 背景颜色：
  - `bg-page`: 页面背景色
  - `bg-card`: 卡片背景色
  - `bg-tag`: 标签背景色
  - `bg-search`: 搜索框背景色

### 兼容旧类名

为了兼容旧代码，我们保留了一些旧的类名，但它们现在使用新的颜色变量：

- `text-333` → `$text-base`
- `text-666` → `$text-secondary`
- `text-info` → `$text-grey`
- `bg-f5` → `$bg-page`

此外，为了提高代码可读性，我们还提供了 `color-*` 类名作为 `text-*` 的别名：

- `color-main` → `text-base`
- `color-secondary` → `text-secondary`
- `color-info` → `text-info`
- `color-grey` → `text-grey`
- `color-disable` → `text-disable`
- `color-inverse` → `text-inverse`

## 如何替换颜色

当需要更新组件或页面的颜色时，请按照以下原则进行替换：

1. **文本颜色**:
   - #333/#333333 → `$text-base` 或使用 `text-base` 类
   - #666/#666666 → `$text-secondary` 或使用 `text-secondary` 类
   - #999/#999999 → `$text-grey` 或使用 `text-grey` 类

2. **背景颜色**:
   - 页面背景 #f5f5f5 → `$bg-page` 或使用 `bg-page` 类
   - 卡片背景 #fff/#ffffff → `$bg-card` 或使用 `bg-card` 类
   - 标签背景 → `$bg-tag` 或使用 `bg-tag` 类
   - 搜索框背景 #f9f9f9 → `$bg-search` 或使用 `bg-search` 类

3. **边框颜色**:
   - #f0f0f0/#eee → `$border-color`

4. **强调色**:
   - 使用 `$primary` 及其变体进行强调和突出显示

## 使用示例

### 在模板中使用：

```html
<text class="text-base">这是主文本</text>
<text class="text-secondary">这是次要文本</text>
<text class="text-info">这是描述性文本</text>
<text class="text-grey">这是占位文本</text>
<text class="text-primary">这是强调文本</text>
<view class="bg-card">这是卡片背景</view>
<view class="bg-tag">这是标签背景</view>
```

### 在 SCSS 中使用：

```scss
.my-text {
  color: $text-base;
}

.my-subtitle {
  color: $text-secondary;
}

.my-card {
  background-color: $bg-card;
  border: 1rpx solid $border-color;
}

.my-tag {
  background-color: $bg-tag;
  color: $text-info;
}

.my-button {
  background-color: $primary;
  color: $text-inverse;
  
  &:disabled {
    color: $text-disable;
  }
}
```

## 最佳实践

- 始终使用变量而非硬编码的颜色值
- 为提高可读性，CSS 类使用 `text-*` 而不是 `color-*`
- 根据元素的重要性选择合适的文本颜色
  - 重要内容使用 `text-base`
  - 次要内容使用 `text-secondary`
  - 描述性内容使用 `text-info`
  - 占位内容使用 `text-grey`
- 在适当的场景使用主题色（`$primary`）进行视觉引导和强调
- 保持一致性，一旦选择了某种模式，请在整个项目中保持一致 