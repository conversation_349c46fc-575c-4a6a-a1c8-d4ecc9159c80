<template>
  <view>
    <!-- 意向职位 -->
    <view class="intention-jobs px-20rpx mb-20rpx">
      <view class="intention-header flex justify-between items-center mb-20rpx">
        <text class="text-30rpx font-bold">意向职位</text>
        <view class="flex-x-center">
          <text class="text-26rpx text-secondary mr-20rpx" @tap="showMoreJobs"
            >更多</text
          >
          <view class="add-intention-btn" @tap="navigateToEditIntention">
            <text class="i-carbon-add"></text>
          </view>
        </view>
      </view>
      <view class="intention-container">
        <scroll-view scroll-x class="intention-scroll" show-scrollbar="false">
          <view class="intention-list flex">
            <view
              v-for="(job, index) in intentionJobs"
              :key="index"
              class="intention-item"
              :class="{ 'intention-active': job.active }"
              @tap="selectIntentionJob(index)"
            >
              {{ job.name }}
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 职位筛选栏 -->
    <view
      class="filter-section px-20rpx py-20rpx mb-20rpx sticky top-180rpx bg-white"
    >
      <view class="flex justify-between items-center">
        <view class="filter-tabs flex">
          <view
            v-for="(tab, index) in filterTabs"
            :key="index"
            class="filter-tab mr-30rpx"
            :class="{ 'active-tab': tab.active }"
            @click="handleFilterTabClick(index)"
          >
            <text class="text-30rpx">{{ tab.name }}</text>
          </view>
        </view>
        <view class="filter-more flex items-center" @click="openFilterPopup">
          <text class="text-secondary">筛选</text>
          <text class="i-carbon-filter ml-4rpx text-secondary"></text>
        </view>
      </view>
    </view>

    <!-- 推荐职位 -->
    <view class="recommend-jobs flex-y gap-20rpx mx-20rpx">
      <JobItem
        v-for="(job, index) in recommendJobs"
        :key="index"
        :job="job"
        @click="goToJobDetail(job)"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import JobItem from "@/components/job/JobItem.vue";

const emit = defineEmits(["openFilterPopup"]);

// 意向职位
const intentionJobs = ref([
  { name: "热门", active: true },
  { name: "兼职", active: false },
  { name: "餐饮", active: false },
  { name: "销售", active: false },
  { name: "客服", active: false },
  { name: "行政", active: false },
  { name: "司机", active: false },
  { name: "普工", active: false },
  { name: "技工", active: false },
]);

// 筛选标签
const filterTabs = ref([
  { name: "推荐", active: true },
  { name: "最新", active: false },
  { name: "附近", active: false },
]);

// 推荐职位
const recommendJobs = ref([
  {
    id: "job1001",
    title: "前端开发工程师",
    isUrgent: true,
    salary: "15-25K·13薪",
    area: "海淀区",
    companyLogo: "https://picsum.photos/seed/company1/80/80",
    tags: ["3-5年", "本科", "前端开发", "Vue", "React"],
    companyName: "字节跳动",
    industry: "互联网",
    publishTime: "1小时前",
  },
  {
    id: "job1002",
    title: "销售经理",
    isUrgent: false,
    salary: "10-15K·底薪+提成",
    area: "朝阳区",
    companyLogo: "https://picsum.photos/seed/company2/80/80",
    tags: ["1-3年", "大专", "销售", "市场拓展"],
    companyName: "新东方教育",
    industry: "教育培训",
    publishTime: "3小时前",
  },
  {
    id: "job1003",
    title: "Java开发工程师",
    isUrgent: true,
    salary: "20-35K·14薪",
    area: "中关村",
    companyLogo: "https://picsum.photos/seed/company3/80/80",
    tags: ["5-10年", "本科", "Java", "微服务", "Spring"],
    companyName: "阿里巴巴",
    industry: "互联网",
    publishTime: "昨天",
  },
  {
    id: "job1004",
    title: "UI设计师",
    isUrgent: false,
    salary: "12-18K·13薪",
    area: "西城区",
    companyLogo: "https://picsum.photos/seed/company4/80/80",
    tags: ["3-5年", "本科", "UI设计", "Photoshop", "Sketch"],
    companyName: "腾讯科技",
    industry: "互联网",
    publishTime: "昨天",
  },
  {
    id: "job1005",
    title: "产品经理",
    isUrgent: false,
    salary: "15-25K·13薪",
    area: "海淀区",
    companyLogo: "https://picsum.photos/seed/company5/80/80",
    tags: ["3-5年", "本科", "产品经理", "需求分析", "项目管理"],
    companyName: "字节跳动",
    industry: "互联网",
    publishTime: "昨天",
  },
]);

// 选择意向职位
const selectIntentionJob = (index: number) => {
  intentionJobs.value.forEach((item, idx) => {
    item.active = idx === index;
  });
  // 根据意向职位过滤列表
};

// 查看更多职位
const showMoreJobs = () => {
  uni.navigateTo({
    url: "/pages/job/category",
  });
};

// 导航到意向职位编辑页面
const navigateToEditIntention = () => {
  uni.navigateTo({
    url: "/pages/job/intention-edit",
  });
};

// 处理筛选标签点击
const handleFilterTabClick = (index: number) => {
  filterTabs.value.forEach((tab, idx) => {
    tab.active = idx === index;
  });
  // 根据筛选条件获取数据
};

// 打开筛选弹出层
const openFilterPopup = () => {
  emit("openFilterPopup");
};

// 跳转到职位详情页
const goToJobDetail = (job: any) => {
  uni.navigateTo({
    url: "/pages/job/detail?id=" + job.id,
  });
};
</script>

<style lang="scss" scoped>
// 意向职位样式
.intention-jobs {
  background-color: $bg-card;
  border-radius: $radius-lg;
  padding: $spacing-20;
  position: relative;

  .intention-container {
    position: relative;
  }

  .intention-scroll {
    white-space: nowrap;
    margin-right: 0;

    .intention-list {
      padding: $spacing-10 0;
    }

    .intention-item {
      display: inline-block;
      padding: $spacing-10 $spacing-16;
      margin-right: $spacing-12;
      background-color: $bg-tag;
      color: $text-secondary;
      font-size: $font-size-base;
      border-radius: $radius-lg;
      border: 1rpx solid $border-color;
      transition: all 0.2s;

      &:active {
        transform: scale(0.98);
      }
    }

    .intention-active {
      background-color: $primary-light;
      color: $primary;
      border: 1rpx solid $primary;
      font-weight: 500;
    }
  }

  .add-intention-btn {
    width: 48rpx;
    height: 48rpx;
    border-radius: 24rpx;
    background-color: $primary;
    color: $text-inverse;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 8rpx rgba($primary, 0.3);
    transition: all 0.2s;

    &:active {
      transform: scale(0.95);
    }

    .i-carbon-add {
      font-size: $font-size-md;
    }
  }
}

// 筛选栏样式
.filter-section {
  background-color: $bg-card;
  border-radius: $radius-lg;

  .filter-tab {
    position: relative;
    padding-bottom: $spacing-8;
    color: $text-secondary;
    transition: all 0.2s;

    &:active {
      transform: scale(0.98);
    }
  }

  .active-tab {
    color: $primary;
    font-weight: 600;

    &:after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 30rpx;
      height: 6rpx;
      background-color: $primary;
      border-radius: $radius-sm;
    }
  }

  .filter-more {
    padding: $spacing-8 $spacing-12;
    background-color: $bg-tag;
    border-radius: $radius;
    color: $text-secondary;
    font-size: $font-size-sm;
    transition: all 0.2s;

    &:active {
      background-color: $bg-search;
      transform: scale(0.98);
    }
  }
}
</style>
