<template>
  <view class="commercial-detail-container">
    <!-- 沉浸式导航栏 -->
    <uni-nav-bar
      fixed
      :border="false"
      status-bar
      :title="showNavTitle ? '商铺详情' : ''"
      left-icon="left"
      :background-color="navBgColor"
      :color="navTextColor"
      @clickLeft="goBack"
    />

    <!-- 内容区域 -->
    <scroll-view
      scroll-y
      enable-flex
      class="content-scroll"
      @scroll="handleScroll"
      :scroll-top="scrollTop"
    >
      <!-- 轮播图 -->
      <swiper
        class="image-swiper"
        :indicator-dots="true"
        :autoplay="false"
        :interval="3000"
        :duration="500"
        :circular="true"
        indicator-color="rgba(255, 255, 255, 0.6)"
        indicator-active-color="#FFFFFF"
      >
        <swiper-item
          v-for="(image, index) in commercialDetail.images"
          :key="index"
          @tap="previewImages(index)"
        >
          <image :src="image" mode="aspectFill" class="swiper-image" />
        </swiper-item>
      </swiper>

      <!-- 图片计数 -->
      <view class="image-counter">
        <text
          >{{ currentImageIndex + 1 }}/{{
            commercialDetail.images.length
          }}</text
        >
      </view>

      <!-- VR标签 -->
      <view v-if="commercialDetail.hasVR" class="vr-tag">
        <text class="i-carbon-view text-20rpx mr-6rpx"></text>
        <text>VR看房</text>
      </view>

      <!-- 基本信息卡片 -->
      <view class="info-card">
        <!-- 价格和标签 -->
        <view class="price-section">
          <view class="price-info">
            <text class="price">{{ commercialDetail.price }}</text>
            <text class="price-unit">{{ getPriceUnit() }}</text>
          </view>
          <view class="deal-type">{{ commercialDetail.dealType }}</view>
        </view>

        <!-- 标题 -->
        <view class="title-section">
          <text class="commercial-title">{{ commercialDetail.title }}</text>
        </view>

        <!-- 基本属性 -->
        <view class="attributes">
          <view class="attribute-item">
            <text class="attribute-value">{{ commercialDetail.area }}㎡</text>
            <text class="attribute-label">面积</text>
          </view>
          <view class="attribute-item">
            <text class="attribute-value">{{ commercialDetail.floor }}</text>
            <text class="attribute-label">楼层</text>
          </view>
          <view class="attribute-item">
            <text class="attribute-value">{{ commercialDetail.type }}</text>
            <text class="attribute-label">类型</text>
          </view>
          <view class="attribute-item">
            <text class="attribute-value">{{ commercialDetail.purpose }}</text>
            <text class="attribute-label">用途</text>
          </view>
        </view>

        <!-- 商铺标签 -->
        <view class="tags-section">
          <text
            v-for="(tag, index) in commercialDetail.tags"
            :key="index"
            class="tag-item"
            >{{ tag }}</text
          >
        </view>
      </view>

      <!-- 位置信息 -->
      <view class="section-card">
        <view class="section-header">
          <text class="section-title">位置信息</text>
        </view>
        <view class="location-content">
          <view class="location-info">
            <view class="building-name">{{ commercialDetail.building }}</view>
            <view class="address">
              <text class="i-carbon-location color-grey mr-10rpx"></text>
              <text class="address-text">{{ commercialDetail.address }}</text>
            </view>
          </view>
          <view class="map-container" @tap="openMap">
            <image
              src="/static/images/map-placeholder.png"
              mode="aspectFill"
              class="map-image"
            />
            <view class="map-overlay">
              <text class="i-carbon-map text-36rpx"></text>
            </view>
          </view>
        </view>
      </view>

      <!-- 商铺特点 -->
      <view class="section-card">
        <view class="section-header">
          <text class="section-title">商铺特点</text>
        </view>
        <view class="features-grid">
          <view
            v-for="(feature, index) in commercialDetail.features"
            :key="index"
            class="feature-item"
          >
            <view class="feature-icon-container">
              <text :class="['feature-icon', feature.icon]"></text>
            </view>
            <text class="feature-name">{{ feature.name }}</text>
            <text class="feature-value">{{ feature.value }}</text>
          </view>
        </view>
      </view>

      <!-- 房源描述 -->
      <view class="section-card">
        <view class="section-header">
          <text class="section-title">商铺描述</text>
        </view>
        <view class="description-content">
          <text class="description-text">{{
            commercialDetail.description
          }}</text>
          <view
            class="show-more"
            v-if="
              isDescriptionFolded && commercialDetail.description.length > 100
            "
            @tap="toggleDescription"
          >
            <text class="show-more-text">展开</text>
            <text class="i-carbon-chevron-down text-24rpx ml-4rpx"></text>
          </view>
          <view
            class="show-more"
            v-if="
              !isDescriptionFolded && commercialDetail.description.length > 100
            "
            @tap="toggleDescription"
          >
            <text class="show-more-text">收起</text>
            <text class="i-carbon-chevron-up text-24rpx ml-4rpx"></text>
          </view>
        </view>
      </view>

      <!-- 配套设施 -->
      <view class="section-card">
        <view class="section-header">
          <text class="section-title">配套设施</text>
        </view>
        <view class="facilities-content">
          <view
            v-for="(facility, index) in commercialDetail.facilities"
            :key="index"
            class="facility-item"
          >
            <text :class="['facility-icon', facility.icon]"></text>
            <text class="facility-name">{{ facility.name }}</text>
          </view>
        </view>
      </view>

      <!-- 交易信息 -->
      <view class="section-card">
        <view class="section-header">
          <text class="section-title">交易信息</text>
        </view>
        <view class="deal-info-content">
          <view
            class="deal-info-item"
            v-for="(item, index) in transactionInfo"
            :key="index"
          >
            <text class="deal-info-label">{{ item.label }}</text>
            <text class="deal-info-value">{{ item.value }}</text>
          </view>
        </view>
      </view>

      <!-- 经纪人信息 -->
      <view class="section-card">
        <view class="section-header">
          <text class="section-title">经纪人信息</text>
        </view>
        <view class="agent-content">
          <view class="agent-info">
            <image
              :src="commercialDetail.agent.avatar"
              mode="aspectFill"
              class="agent-avatar"
            />
            <view class="agent-details">
              <view class="agent-name">{{ commercialDetail.agent.name }}</view>
              <view class="agent-company">{{
                commercialDetail.agent.company
              }}</view>
            </view>
          </view>
          <view class="agent-stats">
            <view class="stat-item">
              <text class="stat-value">{{
                commercialDetail.agent.responseRate
              }}</text>
              <text class="stat-label">回复率</text>
            </view>
            <view class="stat-item">
              <text class="stat-value">{{
                commercialDetail.agent.responseTime
              }}</text>
              <text class="stat-label">回复时间</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 相似商铺 -->
      <view class="section-card">
        <view class="section-header">
          <text class="section-title">相似商铺推荐</text>
          <view class="more-link" @tap="navigateToList">
            <text class="more-text">查看更多</text>
            <text class="i-carbon-arrow-right text-24rpx ml-4rpx"></text>
          </view>
        </view>
        <scroll-view
          scroll-x
          class="similar-shops-scroll"
          show-scrollbar="false"
        >
          <view class="similar-shops-content">
            <view
              v-for="(shop, index) in similarShops"
              :key="index"
              class="similar-shop-card"
              @tap="navigateToDetail(shop.id)"
            >
              <image
                :src="shop.image"
                mode="aspectFill"
                class="similar-shop-image"
              />
              <view class="similar-shop-info">
                <text class="similar-shop-title">{{ shop.title }}</text>
                <text class="similar-shop-details"
                  >{{ shop.area }}㎡ | {{ shop.type }}</text
                >
                <view class="similar-shop-price">
                  <text class="similar-price-value">{{ shop.price }}</text>
                  <text class="similar-price-unit">{{ shop.priceUnit }}</text>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 底部占位 -->
      <view class="bottom-placeholder"></view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-action-bar">
      <view class="action-buttons">
        <view class="action-button" @tap="toggleCollect">
          <text
            :class="[
              'action-icon',
              isCollected
                ? 'i-carbon-star-filled text-primary'
                : 'i-carbon-star color-grey',
            ]"
          ></text>
          <text class="action-text" :class="{ 'text-primary': isCollected }">{{
            isCollected ? "已收藏" : "收藏"
          }}</text>
        </view>
        <view class="action-button" @tap="shareCommercial">
          <text class="action-icon i-carbon-share color-grey"></text>
          <text class="action-text">分享</text>
        </view>
      </view>
      <view class="contact-buttons">
        <view class="contact-button phone-button" @tap="callPhone">
          <text class="i-carbon-phone mr-10rpx"></text>
          <text>电话联系</text>
        </view>
        <view class="contact-button message-button" @tap="sendMessage">
          <text class="i-carbon-chat mr-10rpx"></text>
          <text>在线咨询</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";

// 获取页面参数
const commercialId = ref("");
const currentImageIndex = ref(0);
const isDescriptionFolded = ref(true);
const isCollected = ref(false);
const scrollTop = ref(0);

// 导航栏状态
const showNavTitle = ref(false);
const navBgColor = ref("rgba(255, 255, 255, 0)");
const navTextColor = ref("#ffffff");

// 处理页面滚动
const handleScroll = (e: any) => {
  const scrollTop = e.detail.scrollTop;
  // 滚动距离超过轮播图高度的一半时，显示导航栏背景和标题
  if (scrollTop > 200) {
    navBgColor.value = "#ffffff";
    navTextColor.value = "#000000";
    showNavTitle.value = true;
  } else {
    // 计算透明度，实现渐变效果
    const opacity = Math.min(scrollTop / 200, 1).toFixed(2);
    navBgColor.value = `rgba(255, 255, 255, ${opacity})`;
    navTextColor.value = scrollTop > 100 ? "#000000" : "#ffffff";
    showNavTitle.value = scrollTop > 100;
  }
};

// 页面加载时获取参数
onLoad((options) => {
  if (options.id) {
    commercialId.value = options.id;
    console.log("获取到商铺ID:", commercialId.value);
    // 实际项目中应该根据ID从服务器获取房源详情
    // 此处使用模拟数据
  }
});

// 模拟商铺详情数据
const commercialDetail = ref({
  id: "1",
  title: "大型商业街底商 临街旺铺 适合餐饮",
  building: "万科商业广场",
  area: "120",
  floor: "1层",
  direction: "朝南",
  price: "35000",
  priceType: "rent", // rent or sale
  dealType: "出租",
  type: "商铺",
  purpose: "餐饮、零售",
  address: "北京市朝阳区建国路88号",
  location: "朝阳区",
  tags: ["临街铺位", "繁华商圈", "人流量大", "独立产权", "适合餐饮"],
  images: [
    "https://picsum.photos/seed/shop1/750/500",
    "https://picsum.photos/seed/shop2/750/500",
    "https://picsum.photos/seed/shop3/750/500",
    "https://picsum.photos/seed/shop4/750/500",
  ],
  hasVR: true,
  rentFee: "包含物业费",
  transferFee: "无转让费",
  rentTerm: "3-5年",
  paymentMethod: "押一付三",
  decoration: "简装",
  features: [
    { name: "楼层", value: "1层", icon: "i-carbon-building" },
    { name: "面宽", value: "8米", icon: "i-carbon-ruler" },
    { name: "进深", value: "15米", icon: "i-carbon-arrow-down" },
    { name: "层高", value: "4.5米", icon: "i-carbon-pan" },
    { name: "门宽", value: "3米", icon: "i-carbon-door" },
    { name: "使用率", value: "92%", icon: "i-carbon-percentage" },
  ],
  description:
    "该商铺位于万科商业广场，是临街一层商铺，地理位置优越，周边环境成熟，人流量大。商铺前方有大型停车场，交通便利。适合餐饮、零售等多种业态。\n\n商铺特点：\n1. 临街旺铺，展示面大；\n2. 商圈成熟，人流量大；\n3. 餐饮集中区，业态互补；\n4. 大型社区环绕，消费人群稳定；\n5. 配套齐全，交通便利。",
  facilities: [
    { name: "水电", icon: "i-carbon-battery-charging" },
    { name: "空调", icon: "i-carbon-air-conditioner" },
    { name: "排烟", icon: "i-carbon-smoke" },
    { name: "排污", icon: "i-carbon-rain-drop" },
    { name: "燃气", icon: "i-carbon-fire" },
    { name: "货梯", icon: "i-carbon-delivery" },
    { name: "中央空调", icon: "i-carbon-temperature-hot" },
    { name: "网络", icon: "i-carbon-wifi" },
  ],
  agent: {
    name: "王经理",
    avatar: "https://picsum.photos/seed/agent1/100/100",
    company: "链家地产",
    responseRate: "95%",
    responseTime: "1小时内",
  },
});

// 交易信息
const transactionInfo = computed(() => {
  const info = [];
  if (commercialDetail.value.priceType === "rent") {
    info.push(
      {
        label: "付款方式",
        value: commercialDetail.value.paymentMethod || "押一付三",
      },
      {
        label: "物业费用",
        value: commercialDetail.value.rentFee || "包含物业费",
      },
      { label: "租期要求", value: commercialDetail.value.rentTerm || "3-5年" },
      {
        label: "转让费",
        value: commercialDetail.value.transferFee || "无转让费",
      },
      { label: "装修情况", value: commercialDetail.value.decoration || "简装" }
    );
  } else {
    info.push(
      { label: "产权年限", value: "50年" },
      { label: "物业费用", value: "3元/㎡/月" },
      { label: "装修情况", value: commercialDetail.value.decoration || "简装" },
      { label: "产权证", value: "有证" }
    );
  }
  return info;
});

// 相似商铺推荐
const similarShops = ref([
  {
    id: "2",
    title: "商业中心旺铺 适合零售",
    area: "85",
    type: "商铺",
    price: "28000",
    priceUnit: "元/月",
    image: "https://picsum.photos/seed/similar1/300/200",
  },
  {
    id: "3",
    title: "写字楼配套底商 交通便利",
    area: "150",
    type: "商铺",
    price: "42000",
    priceUnit: "元/月",
    image: "https://picsum.photos/seed/similar2/300/200",
  },
  {
    id: "4",
    title: "社区门口临街商铺 超高人流",
    area: "100",
    type: "商铺",
    price: "32000",
    priceUnit: "元/月",
    image: "https://picsum.photos/seed/similar3/300/200",
  },
]);

// 获取价格单位
const getPriceUnit = () => {
  return commercialDetail.value.priceType === "rent" ? "元/月" : "万元";
};

// 预览图片
const previewImages = (index: number) => {
  currentImageIndex.value = index;
  uni.previewImage({
    current: commercialDetail.value.images[index],
    urls: commercialDetail.value.images,
  });
};

// 展开/收起描述
const toggleDescription = () => {
  isDescriptionFolded.value = !isDescriptionFolded.value;
};

// 打开地图
const openMap = () => {
  uni.navigateTo({
    url: `/pages/house/map?id=${
      commercialId.value
    }&building=${encodeURIComponent(
      commercialDetail.value.building
    )}&address=${encodeURIComponent(commercialDetail.value.address)}`,
  });
};

// 收藏商铺
const toggleCollect = () => {
  isCollected.value = !isCollected.value;
  uni.showToast({
    title: isCollected.value ? "已收藏" : "已取消收藏",
    icon: "none",
  });
};

// 分享商铺
const shareCommercial = () => {
  uni.showActionSheet({
    itemList: ["分享给朋友", "生成分享图片", "复制链接"],
    success: function (res) {
      uni.showToast({
        title: "分享功能开发中",
        icon: "none",
      });
    },
  });
};

// 电话联系
const callPhone = () => {
  uni.showModal({
    title: "联系经纪人",
    content: "确定要拨打经纪人电话吗？",
    success: function (res) {
      if (res.confirm) {
        uni.makePhoneCall({
          phoneNumber: "13888888888", // 实际应从房源数据中获取
          fail: function () {
            uni.showToast({
              title: "拨号失败，请手动拨打",
              icon: "none",
            });
          },
        });
      }
    },
  });
};

// 在线咨询
const sendMessage = () => {
  uni.navigateTo({
    url: `/pages/message/chat?targetId=${commercialDetail.value.agent.name}&targetType=agent&commercialId=${commercialId.value}`,
  });
};

// 前往商铺列表
const navigateToList = () => {
  uni.navigateTo({
    url: "/pages/house/commercial/list",
  });
};

// 前往其他商铺详情
const navigateToDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/house/detail/commercial?id=${id}`,
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.commercial-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

.content-scroll {
  height: calc(100vh - 100rpx);
  background-color: #f5f5f5;
}

/* 轮播图区域 */
.image-swiper {
  width: 100%;
  height: 500rpx;
}

.swiper-image {
  width: 100%;
  height: 100%;
}

.image-counter {
  position: absolute;
  right: 30rpx;
  top: calc(44px + env(safe-area-inset-top) + 20rpx);
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  z-index: 10;
}

.vr-tag {
  position: absolute;
  left: 30rpx;
  top: calc(44px + env(safe-area-inset-top) + 20rpx);
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  z-index: 10;
  display: flex;
  align-items: center;
}

/* 信息卡片 */
.info-card {
  margin: 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.price-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.price-info {
  display: flex;
  align-items: baseline;
}

.price {
  font-size: 48rpx;
  font-weight: bold;
  color: #ff6d00;
}

.price-unit {
  font-size: 28rpx;
  color: #ff6d00;
  margin-left: 6rpx;
}

.deal-type {
  font-size: 28rpx;
  color: #ffffff;
  background-color: #ff6d00;
  padding: 6rpx 16rpx;
  border-radius: 6rpx;
}

.title-section {
  margin-bottom: 30rpx;
}

.commercial-title {
  font-size: 36rpx;
  font-weight: bold;
  line-height: 1.4;
}

.attributes {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.attribute-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.attribute-value {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.attribute-label {
  font-size: 24rpx;
  color: #999;
}

.tags-section {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag-item {
  font-size: 24rpx;
  color: #666;
  background-color: #f5f5f5;
  padding: 6rpx 16rpx;
  border-radius: 6rpx;
}

/* 内容区域卡片 */
.section-card {
  margin: 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
}

.more-link {
  display: flex;
  align-items: center;
}

.more-text {
  font-size: 26rpx;
  color: #999;
}

/* 位置信息 */
.location-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.location-info {
  flex: 1;
}

.building-name {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.address {
  display: flex;
  align-items: center;
}

.address-text {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}

.map-container {
  width: 200rpx;
  height: 140rpx;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  margin-left: 20rpx;
}

.map-image {
  width: 100%;
  height: 100%;
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
}

/* 商铺特点 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.feature-icon-container {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(255, 109, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 12rpx;
}

.feature-icon {
  font-size: 40rpx;
  color: #ff6d00;
}

.feature-name {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 6rpx;
}

.feature-value {
  font-size: 28rpx;
  font-weight: 500;
}

/* 房源描述 */
.description-content {
  position: relative;
}

.description-text {
  font-size: 28rpx;
  line-height: 1.6;
  text-align: justify;
  max-height: v-bind('isDescriptionFolded ? "200rpx" : "auto"');
  overflow: v-bind('isDescriptionFolded ? "hidden" : "visible"');
  white-space: pre-line;
}

.show-more {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20rpx;
}

.show-more-text {
  font-size: 26rpx;
  color: #ff6d00;
}

/* 配套设施 */
.facilities-content {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30rpx;
}

.facility-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.facility-icon {
  font-size: 44rpx;
  color: #ff6d00;
  margin-bottom: 10rpx;
}

.facility-name {
  font-size: 24rpx;
  color: #666;
}

/* 交易信息 */
.deal-info-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.deal-info-item {
  display: flex;
  flex-direction: column;
}

.deal-info-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.deal-info-value {
  font-size: 28rpx;
  font-weight: 500;
}

/* 经纪人信息 */
.agent-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.agent-info {
  display: flex;
  align-items: center;
}

.agent-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.agent-name {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.agent-company {
  font-size: 24rpx;
  color: #999;
}

.agent-stats {
  display: flex;
  gap: 20rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 6rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #999;
}

/* 相似商铺 */
.similar-shops-scroll {
  width: 100%;
}

.similar-shops-content {
  display: flex;
  padding: 10rpx 0;
}

.similar-shop-card {
  width: 280rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #f9f9f9;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.similar-shop-image {
  width: 100%;
  height: 180rpx;
}

.similar-shop-info {
  padding: 16rpx;
}

.similar-shop-title {
  font-size: 26rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.similar-shop-details {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.similar-shop-price {
  display: flex;
  align-items: baseline;
}

.similar-price-value {
  font-size: 28rpx;
  font-weight: 500;
  color: #ff6d00;
}

.similar-price-unit {
  font-size: 22rpx;
  color: #ff6d00;
  margin-left: 4rpx;
}

/* 底部占位 */
.bottom-placeholder {
  height: 100rpx;
}

/* 底部操作栏 */
.bottom-action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  box-shadow: 0 -2rpx 20rpx rgba(0, 0, 0, 0.1);
  padding-bottom: env(safe-area-inset-bottom);
}

.action-buttons {
  display: flex;
  width: 30%;
  height: 100%;
}

.action-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.action-icon {
  font-size: 40rpx;
  margin-bottom: 6rpx;
}

.action-text {
  font-size: 22rpx;
  color: #666;
}

.contact-buttons {
  display: flex;
  width: 70%;
  height: 100%;
}

.contact-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  margin: 0 10rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.phone-button {
  background-color: rgba(255, 109, 0, 0.1);
  color: #ff6d00;
  border: 1px solid #ff6d00;
}

.message-button {
  background-color: #ff6d00;
  color: white;
}

/* 通用样式 */
.text-primary {
  color: #ff6d00;
}

.color-grey {
  color: #999;
}

.mr-4rpx {
  margin-right: 4rpx;
}

.mr-6rpx {
  margin-right: 6rpx;
}

.mr-10rpx {
  margin-right: 10rpx;
}

.ml-4rpx {
  margin-left: 4rpx;
}
</style>
