<template>
  <view class="search-page">
    <!-- 搜索栏 -->
    <view class="search-header">
      <view class="search-bar">
        <view class="search-input-wrapper">
          <text class="i-carbon-search search-icon"></text>
          <input
            v-model="searchKeyword"
            placeholder="搜索用户昵称、职业、兴趣"
            class="search-input"
            @input="onSearchInput"
          />
        </view>
        <view class="filter-btn" @click="showFilterModal = true">
          <text class="i-carbon-filter"></text>
          <text>筛选</text>
        </view>
      </view>
    </view>

    <!-- 筛选条件展示 -->
    <view v-if="hasActiveFilters" class="active-filters">
      <view class="filter-tags">
        <view
          v-for="(filter, index) in activeFilterTags"
          :key="index"
          class="filter-tag"
          @click="removeFilter(filter.type)"
        >
          {{ filter.label }}
          <text class="i-carbon-close ml-5rpx"></text>
        </view>
        <view class="clear-all" @click="clearAllFilters"> 清空筛选 </view>
      </view>
    </view>

    <!-- 搜索结果 -->
    <scroll-view
      scroll-y
      class="search-results"
      refresher-enabled
      @refresherrefresh="onRefresh"
      :refresher-triggered="refreshing"
    >
      <view v-if="searchResults.length === 0 && !loading" class="empty-state">
        <text class="i-carbon-search-locate empty-icon"></text>
        <text class="empty-text">暂无搜索结果</text>
        <text class="empty-desc">试试调整搜索条件</text>
      </view>

      <view v-else class="user-list">
        <view
          v-for="user in searchResults"
          :key="user.id"
          class="user-item"
          @click="viewUserDetail(user.id)"
        >
          <image :src="user.avatar" class="user-avatar" mode="aspectFill" />
          <view class="user-info">
            <view class="user-basic">
              <text class="user-name">{{ user.name }}</text>
              <text class="user-age">{{ user.age }}岁</text>
              <view v-if="user.isVerified" class="verified-badge">
                <text class="i-carbon-checkmark"></text>
              </view>
            </view>
            <view class="user-details">
              <text class="user-job">{{ user.job }}</text>
              <text class="separator">·</text>
              <text class="user-education">{{ user.education }}</text>
            </view>
            <view class="user-tags">
              <view
                v-for="(tag, tagIndex) in user.tags.slice(0, 3)"
                :key="tagIndex"
                class="user-tag"
              >
                {{ tag }}
              </view>
            </view>
            <view class="user-location">
              <text class="i-carbon-location"></text>
              <text>{{ user.distance }}</text>
            </view>
          </view>
          <view class="user-actions">
            <view class="action-btn like-btn" @click.stop="likeUser(user.id)">
              <text class="i-carbon-favorite"></text>
            </view>
            <view
              class="action-btn chat-btn"
              @click.stop="chatWithUser(user.id)"
            >
              <text class="i-carbon-chat"></text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 筛选弹窗 -->
    <view
      v-if="showFilterModal"
      class="filter-modal"
      @click="showFilterModal = false"
    >
      <view class="filter-content" @click.stop>
        <view class="filter-header">
          <text class="filter-title">筛选条件</text>
          <text class="i-carbon-close" @click="showFilterModal = false"></text>
        </view>

        <scroll-view scroll-y class="filter-body">
          <!-- 年龄筛选 -->
          <view class="filter-section">
            <text class="section-title">年龄</text>
            <view class="age-range">
              <slider
                :value="filters.ageRange[0]"
                min="18"
                max="60"
                step="1"
                activeColor="#ff5778"
                backgroundColor="#f0f0f0"
                block-size="20"
                @change="onAgeChange"
              />
              <view class="age-display">
                {{ filters.ageRange[0] }}岁 - {{ filters.ageRange[1] }}岁
              </view>
            </view>
          </view>

          <!-- 性别筛选 -->
          <view class="filter-section">
            <text class="section-title">性别</text>
            <view class="gender-options">
              <view
                v-for="gender in genderOptions"
                :key="gender.value"
                class="option-item"
                :class="{ active: filters.gender === gender.value }"
                @click="filters.gender = gender.value"
              >
                {{ gender.label }}
              </view>
            </view>
          </view>

          <!-- 学历筛选 -->
          <view class="filter-section">
            <text class="section-title">学历</text>
            <view class="education-options">
              <view
                v-for="edu in educationOptions"
                :key="edu.value"
                class="option-item"
                :class="{ active: filters.education.includes(edu.value) }"
                @click="toggleEducation(edu.value)"
              >
                {{ edu.label }}
              </view>
            </view>
          </view>

          <!-- 距离筛选 -->
          <view class="filter-section">
            <text class="section-title">距离</text>
            <view class="distance-options">
              <view
                v-for="dist in distanceOptions"
                :key="dist.value"
                class="option-item"
                :class="{ active: filters.distance === dist.value }"
                @click="filters.distance = dist.value"
              >
                {{ dist.label }}
              </view>
            </view>
          </view>
        </scroll-view>

        <view class="filter-footer">
          <view class="reset-btn" @click="resetFilters">重置</view>
          <view class="confirm-btn" @click="applyFilters">确定</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";

// 搜索相关
const searchKeyword = ref("");
const searchResults = ref([]);
const loading = ref(false);
const refreshing = ref(false);

// 筛选相关
const showFilterModal = ref(false);
const filters = ref({
  ageRange: [20, 35],
  gender: "all",
  education: [],
  distance: "all",
});

// 筛选选项
const genderOptions = [
  { label: "不限", value: "all" },
  { label: "男", value: "male" },
  { label: "女", value: "female" },
];

const educationOptions = [
  { label: "高中", value: "high_school" },
  { label: "大专", value: "college" },
  { label: "本科", value: "bachelor" },
  { label: "硕士", value: "master" },
  { label: "博士", value: "doctor" },
];

const distanceOptions = [
  { label: "不限", value: "all" },
  { label: "1km内", value: "1" },
  { label: "5km内", value: "5" },
  { label: "10km内", value: "10" },
  { label: "同城", value: "city" },
];

// 模拟搜索结果数据
const mockUsers = ref([
  {
    id: "1001",
    name: "小雅",
    age: 26,
    avatar: "https://picsum.photos/200/300?random=1",
    job: "设计师",
    education: "本科",
    tags: ["设计", "旅行", "摄影"],
    distance: "2.3km",
    isVerified: true,
  },
  {
    id: "1002",
    name: "小明",
    age: 28,
    avatar: "https://picsum.photos/200/300?random=2",
    job: "程序员",
    education: "硕士",
    tags: ["编程", "游戏", "运动"],
    distance: "1.8km",
    isVerified: false,
  },
]);

// 计算属性
const hasActiveFilters = computed(() => {
  return (
    filters.value.gender !== "all" ||
    filters.value.education.length > 0 ||
    filters.value.distance !== "all" ||
    filters.value.ageRange[0] !== 20 ||
    filters.value.ageRange[1] !== 35
  );
});

const activeFilterTags = computed(() => {
  const tags = [];
  if (filters.value.gender !== "all") {
    const gender = genderOptions.find((g) => g.value === filters.value.gender);
    tags.push({ type: "gender", label: gender?.label });
  }
  if (filters.value.education.length > 0) {
    filters.value.education.forEach((edu) => {
      const education = educationOptions.find((e) => e.value === edu);
      tags.push({ type: "education", label: education?.label });
    });
  }
  if (filters.value.distance !== "all") {
    const distance = distanceOptions.find(
      (d) => d.value === filters.value.distance
    );
    tags.push({ type: "distance", label: distance?.label });
  }
  return tags;
});

// 方法
const onSearchInput = () => {
  // 实际项目中这里应该调用搜索API
  performSearch();
};

const performSearch = () => {
  loading.value = true;
  setTimeout(() => {
    searchResults.value = mockUsers.value.filter(
      (user) =>
        user.name.includes(searchKeyword.value) ||
        user.job.includes(searchKeyword.value) ||
        user.tags.some((tag) => tag.includes(searchKeyword.value))
    );
    loading.value = false;
  }, 500);
};

const onRefresh = () => {
  refreshing.value = true;
  setTimeout(() => {
    refreshing.value = false;
  }, 1000);
};

const toggleEducation = (value: string) => {
  const index = filters.value.education.indexOf(value);
  if (index > -1) {
    filters.value.education.splice(index, 1);
  } else {
    filters.value.education.push(value);
  }
};

const removeFilter = (type: string) => {
  if (type === "gender") {
    filters.value.gender = "all";
  } else if (type === "distance") {
    filters.value.distance = "all";
  } else if (type === "education") {
    filters.value.education = [];
  }
  applyFilters();
};

const clearAllFilters = () => {
  resetFilters();
  applyFilters();
};

const resetFilters = () => {
  filters.value = {
    ageRange: [20, 35],
    gender: "all",
    education: [],
    distance: "all",
  };
};

const applyFilters = () => {
  showFilterModal.value = false;
  performSearch();
};

const onAgeChange = (e: any) => {
  filters.value.ageRange[0] = e.detail.value;
  // 确保最大值不小于最小值
  if (filters.value.ageRange[1] < filters.value.ageRange[0]) {
    filters.value.ageRange[1] = filters.value.ageRange[0] + 5;
  }
};

const viewUserDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/dating/user/detail?id=${id}`,
  });
};

const likeUser = (id: string) => {
  uni.showToast({
    title: "已喜欢",
    icon: "none",
  });
};

const chatWithUser = (id: string) => {
  uni.navigateTo({
    url: `/pages/dating/chat/conversation?userId=${id}`,
  });
};

onMounted(() => {
  searchResults.value = mockUsers.value;
});
</script>

<style lang="scss" scoped>
.search-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.search-header {
  background: white;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.search-bar {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.search-input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 25rpx;
  padding: 0 20rpx;
  height: 70rpx;
}

.search-icon {
  color: #999;
  font-size: 32rpx;
  margin-right: 15rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  color: #ff5778;
  font-size: 28rpx;
}

.active-filters {
  background: white;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  align-items: center;
}

.filter-tag {
  background: #ff5778;
  color: white;
  font-size: 24rpx;
  padding: 8rpx 15rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
}

.clear-all {
  color: #999;
  font-size: 24rpx;
  text-decoration: underline;
}

.search-results {
  flex: 1;
  padding: 20rpx 30rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 120rpx;
  color: #ddd;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
}

.user-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.user-item {
  background: white;
  border-radius: 15rpx;
  padding: 20rpx;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 15rpx;
  flex-shrink: 0;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.user-basic {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
}

.user-age {
  font-size: 26rpx;
  color: #666;
}

.verified-badge {
  background: #ff5778;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
}

.user-details {
  display: flex;
  align-items: center;
  gap: 10rpx;
  font-size: 26rpx;
  color: #666;
}

.separator {
  color: #ddd;
}

.user-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.user-tag {
  background: #f0f0f0;
  color: #666;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 15rpx;
}

.user-location {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #999;
}

.user-actions {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  align-items: center;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.like-btn {
  background: #ffe6ea;
  color: #ff5778;
}

.chat-btn {
  background: #e6f3ff;
  color: #4a90e2;
}

.filter-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.filter-content {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  width: 100%;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-title {
  font-size: 34rpx;
  font-weight: bold;
}

.filter-body {
  flex: 1;
  padding: 30rpx;
}

.filter-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: block;
}

.age-range {
  padding: 20rpx 0;
}

.age-display {
  text-align: center;
  font-size: 26rpx;
  color: #666;
  margin-top: 15rpx;
}

.gender-options,
.education-options,
.distance-options {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.option-item {
  background: #f5f5f5;
  color: #666;
  font-size: 26rpx;
  padding: 15rpx 25rpx;
  border-radius: 25rpx;
  transition: all 0.3s;

  &.active {
    background: #ff5778;
    color: white;
  }
}

.filter-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.reset-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 25rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
}

.reset-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: #ff5778;
  color: white;
}
</style>
