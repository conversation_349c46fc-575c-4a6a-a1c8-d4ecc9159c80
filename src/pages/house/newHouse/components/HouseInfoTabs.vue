<template>
  <view class="house-info-tabs">
    <!-- 标签页头部 -->
    <view class="tabs-header">
      <view
        v-for="(tab, index) in tabs"
        :key="index"
        class="tab-item"
        :class="{ active: activeTabIndex === index }"
        @tap="switchTab(index)"
      >
        <text class="tab-text">{{ tab.title }}</text>
      </view>
    </view>

    <!-- 标签页内容区域 -->
    <view class="tabs-content">
      <view class="tab-pane" v-if="activeTabIndex === 0">
        <slot name="basic-info"></slot>
      </view>
      <view class="tab-pane" v-if="activeTabIndex === 1">
        <slot name="sales-info"></slot>
      </view>
      <view class="tab-pane" v-if="activeTabIndex === 2">
        <slot name="community-info"></slot>
      </view>
      <view class="tab-pane" v-if="activeTabIndex === 3">
        <slot name="license-info"></slot>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";

const props = defineProps({
  // 初始激活的标签页索引
  initialTabIndex: {
    type: Number,
    default: 0,
  },
});

// 标签页数据
const tabs = [
  { title: "基础信息", key: "basic" },
  { title: "销售信息", key: "sales" },
  { title: "小区概况", key: "community" },
  { title: "预售许可证", key: "license" },
];

// 当前激活的标签页索引
const activeTabIndex = ref(props.initialTabIndex);

// 切换标签页
const switchTab = (index: number) => {
  activeTabIndex.value = index;
};

// 对外暴露当前激活的标签页
defineExpose({
  activeTabIndex,
  switchTab,
});
</script>

<style lang="scss" scoped>
.house-info-tabs {
  width: 100%;
  background-color: #feffff;

  .tabs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 88rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .tab-item {
      flex: 1;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .tab-text {
        font-size: 32rpx;
        color: #71717a;
        padding: 0 4rpx;
      }

      &.active {
        .tab-text {
          color: #212529;
          font-weight: 600;
        }

        &::after {
          content: "";
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 60rpx;
          height: 6rpx;
          background-color: #3b7fff;
          border-radius: 2rpx;
        }
      }
    }
  }

  .tabs-content {
    padding: 12rpx;

    .tab-pane {
      min-height: 200rpx;
    }
  }
}
</style>
