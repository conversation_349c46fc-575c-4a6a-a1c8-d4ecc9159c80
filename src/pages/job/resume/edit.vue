<template>
  <view class="edit-container safe-area-inset-bottom">
    <!-- 导航栏 -->
    <uni-nav-bar
      :fixed="true"
      :border="false"
      status-bar
      :title="getPageTitle"
      left-icon="left"
      @clickLeft="goBack"
    />

    <!-- 编辑表单 -->
    <scroll-view scroll-y enable-flex class="edit-content">
      <!-- 个人信息编辑 -->
      <view v-if="section === 'personal'" class="edit-form">
        <view class="section-title">基本信息</view>

        <view class="form-group">
          <view class="form-label required">
            <text>姓名</text>
            <text class="required-mark">*</text>
          </view>
          <input
            class="form-input"
            type="text"
            v-model="formData.name"
            placeholder="请输入姓名"
          />
        </view>

        <view class="form-group">
          <view class="form-label required">
            <text>性别</text>
            <text class="required-mark">*</text>
          </view>
          <view class="radio-group">
            <view
              class="radio-item"
              :class="{ active: formData.gender === '男' }"
              @tap="formData.gender = '男'"
            >
              <text
                class="radio-icon i-carbon-radio-button"
                v-if="formData.gender !== '男'"
              ></text>
              <text
                class="radio-icon i-carbon-radio-button-checked"
                v-else
              ></text>
              <text>男</text>
            </view>
            <view
              class="radio-item"
              :class="{ active: formData.gender === '女' }"
              @tap="formData.gender = '女'"
            >
              <text
                class="radio-icon i-carbon-radio-button"
                v-if="formData.gender !== '女'"
              ></text>
              <text
                class="radio-icon i-carbon-radio-button-checked"
                v-else
              ></text>
              <text>女</text>
            </view>
          </view>
        </view>

        <view class="form-group">
          <view class="form-label">
            <text>生日</text>
          </view>
          <picker
            mode="date"
            :value="formData.birthday"
            start="1950-01-01"
            end="2010-12-31"
            @change="onBirthdayChange"
          >
            <view class="picker-view">
              <text>{{ formData.birthday || "请选择生日" }}</text>
              <text class="i-carbon-chevron-down"></text>
            </view>
          </picker>
        </view>

        <view class="section-title">联系方式</view>

        <view class="form-group">
          <view class="form-label required">
            <text>手机号码</text>
            <text class="required-mark">*</text>
          </view>
          <input
            class="form-input"
            type="number"
            v-model="formData.phone"
            placeholder="请输入手机号码"
            maxlength="11"
          />
        </view>

        <view class="section-title">职业信息</view>

        <view class="form-group">
          <view class="form-label">
            <text>工作经验</text>
          </view>
          <picker
            mode="selector"
            :range="experienceOptions"
            @change="onExperienceChange"
          >
            <view class="picker-view">
              <text>{{ formData.experience || "请选择工作经验" }}</text>
              <text class="i-carbon-chevron-down"></text>
            </view>
          </picker>
        </view>

        <view class="form-group">
          <view class="form-label">
            <text>最高学历</text>
          </view>
          <picker
            mode="selector"
            :range="educationOptions"
            @change="onEducationChange"
          >
            <view class="picker-view">
              <text>{{ formData.education || "请选择最高学历" }}</text>
              <text class="i-carbon-chevron-down"></text>
            </view>
          </picker>
        </view>
      </view>

      <!-- 教育经历编辑 -->
      <view v-else-if="section === 'education'" class="edit-form">
        <view class="section-title">教育信息</view>

        <view class="form-group">
          <view class="form-label">
            <text>学校名称</text>
          </view>
          <input
            class="form-input"
            type="text"
            v-model="formData.school"
            placeholder="请输入学校名称"
          />
        </view>

        <view class="form-group">
          <view class="form-label">
            <text>专业</text>
          </view>
          <input
            class="form-input"
            type="text"
            v-model="formData.major"
            placeholder="请输入专业名称"
          />
        </view>

        <view class="form-group">
          <view class="form-label">
            <text>学历</text>
          </view>
          <picker
            mode="selector"
            :range="educationOptions"
            @change="onDegreeChange"
          >
            <view class="picker-view">
              <text>{{ formData.degree || "请选择学历" }}</text>
              <text class="i-carbon-chevron-down"></text>
            </view>
          </picker>
        </view>

        <view class="form-group">
          <view class="form-label">
            <text class="i-carbon-calendar" style="margin-right: 8rpx"></text>
            <text>起止时间</text>
          </view>
          <view class="time-range-picker">
            <picker
              mode="date"
              fields="month"
              :value="formData.startTime"
              @change="onStartTimeChange"
            >
              <view class="time-picker-item">
                <text>{{ formData.startTime || "开始时间" }}</text>
                <text class="i-carbon-chevron-down"></text>
              </view>
            </picker>
            <text class="time-separator">至</text>
            <picker
              mode="date"
              fields="month"
              :value="formData.endTime"
              @change="onEndTimeChange"
            >
              <view class="time-picker-item">
                <text>{{ formData.endTime || "结束时间" }}</text>
                <text class="i-carbon-chevron-down"></text>
              </view>
            </picker>
          </view>
        </view>
      </view>

      <!-- 工作经验编辑 -->
      <view v-else-if="section === 'experience'" class="edit-form">
        <view class="section-title">工作经历</view>

        <view class="form-group">
          <view class="form-label">
            <text>公司名称</text>
          </view>
          <input
            class="form-input"
            type="text"
            v-model="formData.company"
            placeholder="请输入公司名称"
          />
        </view>

        <view class="form-group">
          <view class="form-label">
            <text>职位名称</text>
          </view>
          <input
            class="form-input"
            type="text"
            v-model="formData.title"
            placeholder="请输入职位名称"
          />
        </view>

        <view class="form-group">
          <view class="form-label">
            <text>起止时间</text>
          </view>
          <view class="time-range-picker">
            <picker
              mode="date"
              fields="month"
              :value="formData.startTime"
              @change="onStartTimeChange"
            >
              <view class="time-picker-item">
                <text>{{ formData.startTime || "开始时间" }}</text>
                <text class="i-carbon-chevron-down"></text>
              </view>
            </picker>
            <text class="time-separator">至</text>
            <view class="time-picker-item" @tap="toggleCurrentJob">
              <text :class="{ active: formData.isCurrentJob }">
                {{
                  formData.isCurrentJob
                    ? "至今"
                    : formData.endTime || "结束时间"
                }}
              </text>
              <text
                class="i-carbon-chevron-down"
                v-if="!formData.isCurrentJob"
              ></text>
            </view>
            <picker
              v-if="!formData.isCurrentJob"
              mode="date"
              fields="month"
              :value="formData.endTime"
              @change="onEndTimeChange"
              style="
                position: absolute;
                right: 0;
                top: 0;
                width: 45%;
                height: 100%;
                opacity: 0;
              "
            >
              <view style="width: 100%; height: 100%"></view>
            </picker>
          </view>
        </view>

        <view class="form-group">
          <view class="form-label">
            <text>工作内容</text>
          </view>
          <view class="work-items">
            <view
              v-for="(responsibility, index) in formData.responsibilities"
              :key="index"
              class="work-item-edit"
            >
              <textarea
                class="form-textarea"
                v-model="formData.responsibilities[index]"
                :placeholder="
                  responsibility ? '编辑工作内容' : '请输入工作内容'
                "
              />
              <view class="delete-btn" @tap="deleteWorkItem(index)">
                <text class="i-carbon-trash-can"></text>
              </view>
            </view>
            <view class="add-work-item" @tap="addWorkItem">
              <text class="i-carbon-add"></text>
              <text>添加工作内容</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 技能编辑 -->
      <view v-else-if="section === 'skills'" class="edit-form">
        <view class="section-title">专业技能</view>

        <view class="form-group">
          <view class="form-label">
            <text>技能标签</text>
            <text class="skill-count-hint">（最多5个标签）</text>
          </view>
          <view class="skills-edit">
            <view
              v-for="(skill, index) in formData.skills"
              :key="index"
              class="skill-tag-edit"
            >
              <text>{{ skill }}</text>
              <text class="delete-icon" @tap="deleteSkill(index)">×</text>
            </view>
            <view
              class="add-skill"
              @tap="showAddSkillInput"
              :class="{ disabled: formData.skills.length >= maxSkillCount }"
            >
              <text class="i-carbon-add"></text>
              <text>{{
                formData.skills.length >= maxSkillCount
                  ? "已达上限"
                  : "添加技能"
              }}</text>
            </view>
          </view>
          <view
            v-if="formData.skills.length >= maxSkillCount"
            class="skill-limit-tip"
          >
            <text class="i-carbon-information"></text>
            <text>最多只能添加{{ maxSkillCount }}个技能标签</text>
          </view>
        </view>

        <view class="form-group" v-if="showAddSkill">
          <input
            class="form-input"
            type="text"
            v-model="newSkill"
            placeholder="请输入技能名称"
            maxlength="10"
          />
          <view class="skill-actions">
            <view class="cancel-btn" @tap="cancelAddSkill">取消</view>
            <view class="confirm-btn" @tap="addSkill">确认</view>
          </view>
        </view>

        <view class="form-group">
          <view class="form-label">技能描述</view>
          <textarea
            class="form-textarea"
            v-model="formData.skillDescription"
            placeholder="请描述你的技能和专长"
          />
        </view>
      </view>

      <!-- 自我评价编辑 -->
      <view v-else-if="section === 'evaluation'" class="edit-form">
        <view class="section-title">自我评价</view>

        <view class="form-group">
          <view class="form-label">
            <text>个人介绍</text>
          </view>
          <textarea
            class="form-textarea"
            v-model="formData.selfEvaluation"
            placeholder="请输入自我评价"
          />
        </view>
      </view>

      <!-- 底部占位空间，防止内容被底部按钮遮挡 -->
      <view class="bottom-placeholder"></view>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="bottom-actions">
      <view class="action-btn outline-btn" @tap="goBack">
        <text>取消</text>
      </view>
      <view class="action-btn primary-btn" @tap="saveData">
        <text>保存</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
// 获取页面参数
const section = ref("");
const action = ref("");
const index = ref(-1);

// 技能相关
const showAddSkill = ref(false);
const newSkill = ref("");
const maxSkillCount = 5;

// 表单数据
const formData = reactive({
  name: "",
  gender: "",
  birthday: "",
  phone: "",
  email: "",
  experience: "",
  education: "",
  educations: [],
  experiences: [],
  skills: [],
  skillDescription: "",
  selfEvaluation: "",
  responsibilities: [],
  school: "",
  major: "",
  degree: "",
  company: "",
  title: "",
  time: "",
  startTime: "",
  endTime: "",
  isCurrentJob: false,
});

const experienceOptions = [
  "应届毕业生",
  "1年以下",
  "1-3年",
  "3-5年",
  "5-10年",
  "10年以上",
];

const educationOptions = [
  "初中及以下",
  "高中/中专",
  "大专",
  "本科",
  "硕士",
  "博士",
  "其他",
];

// 计算页面标题
const getPageTitle = computed(() => {
  const titles = {
    personal: "编辑个人信息",
    education: "编辑教育经历",
    experience: "编辑工作经验",
    skills: "编辑专业技能",
    evaluation: "编辑自我评价",
  };
  return titles[section.value] || "编辑简历";
});

// 页面生命周期
onLoad((options) => {
  section.value = options.section || "";
  action.value = options.action || "";
  index.value = Number(options.index) || -1;

  // 初始化表单数据
  initFormData();
});

const initFormData = () => {
  // 这里可以从本地存储或接口获取数据
  // 临时模拟数据
  if (section.value === "personal") {
    formData.name = "张小明";
    formData.gender = "男";
    formData.birthday = "1995-01-15";
    formData.phone = "13812345678";
    formData.experience = "3年";
    formData.education = "本科";
  } else if (section.value === "skills") {
    formData.skills = ["HTML/CSS", "JavaScript", "Vue.js"];
    formData.skillDescription = "熟练掌握前端开发技术栈";
  }
  // ... 其他初始化逻辑
};

// 时间选择相关方法
const onStartTimeChange = (e) => {
  const selectedDate = e.detail.value;
  // 将2023-10转换为2023.10格式
  formData.startTime = selectedDate.replace("-", ".");
  updateTimeRange();
};

const onEndTimeChange = (e) => {
  const selectedDate = e.detail.value;
  formData.endTime = selectedDate.replace("-", ".");
  updateTimeRange();
};

const toggleCurrentJob = () => {
  formData.isCurrentJob = !formData.isCurrentJob;
  if (formData.isCurrentJob) {
    formData.endTime = "";
  }
  updateTimeRange();
};

const updateTimeRange = () => {
  if (formData.startTime) {
    if (formData.isCurrentJob) {
      formData.time = `${formData.startTime}-至今`;
    } else if (formData.endTime) {
      formData.time = `${formData.startTime}-${formData.endTime}`;
    } else {
      formData.time = formData.startTime;
    }
  }
};

// 生日选择
const onBirthdayChange = (e) => {
  formData.birthday = e.detail.value;
};

// 工作经验选择
const onExperienceChange = (e) => {
  formData.experience = experienceOptions[e.detail.value];
};

// 学历选择
const onEducationChange = (e) => {
  formData.education = educationOptions[e.detail.value];
};

const onDegreeChange = (e) => {
  formData.degree = educationOptions[e.detail.value];
};

// 工作内容相关方法
const addWorkItem = () => {
  formData.responsibilities.push("");
};

const deleteWorkItem = (index: number) => {
  if (formData.responsibilities.length > 1) {
    formData.responsibilities.splice(index, 1);
  } else {
    uni.showToast({
      title: "至少保留一项工作内容",
      icon: "none",
    });
  }
};

// 技能相关方法
const showAddSkillInput = () => {
  if (formData.skills.length >= maxSkillCount) {
    uni.showToast({
      title: `最多只能添加${maxSkillCount}个技能标签`,
      icon: "none",
    });
    return;
  }
  showAddSkill.value = true;
};

const addSkill = () => {
  if (newSkill.value.trim()) {
    if (formData.skills.length >= maxSkillCount) {
      uni.showToast({
        title: `最多只能添加${maxSkillCount}个技能标签`,
        icon: "none",
      });
      return;
    }

    // 检查是否重复
    if (formData.skills.includes(newSkill.value.trim())) {
      uni.showToast({
        title: "该技能已存在",
        icon: "none",
      });
      return;
    }

    formData.skills.push(newSkill.value.trim());
    newSkill.value = "";
    showAddSkill.value = false;
  } else {
    uni.showToast({
      title: "技能名称不能为空",
      icon: "none",
    });
  }
};

const cancelAddSkill = () => {
  newSkill.value = "";
  showAddSkill.value = false;
};

const deleteSkill = (index: number) => {
  formData.skills.splice(index, 1);
};

// 保存数据
const saveData = () => {
  // 验证必填字段
  if (section.value === "personal") {
    if (!formData.name.trim()) {
      uni.showToast({
        title: "请输入姓名",
        icon: "none",
      });
      return;
    }
    if (!formData.phone.trim()) {
      uni.showToast({
        title: "请输入手机号码",
        icon: "none",
      });
      return;
    }
  }

  // 这里实现保存逻辑
  uni.showToast({
    title: "保存成功",
    icon: "success",
  });

  setTimeout(() => {
    goBack();
  }, 1500);
};

// 返回
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.edit-container {
  min-height: 100vh;
  width: 100%;
  background-color: var(--bg-page);
}

.edit-content {
  height: calc(100vh - 120rpx - env(safe-area-inset-bottom) - 44px);
  width: 100%;
  box-sizing: border-box;
  padding-bottom: env(safe-area-inset-bottom);
}

.edit-form {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-20);
  margin: var(--spacing-12);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-base);
  padding-left: var(--spacing-10);
  border-left: 4rpx solid var(--primary);
  margin: var(--spacing-20) 0;
  display: flex;
  align-items: center;
}

.form-group {
  margin-bottom: var(--spacing-20);
  width: 100%;
}

.form-label {
  margin-bottom: var(--spacing-10);
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  display: flex;
  align-items: center;

  &.required {
    .required-mark {
      color: var(--text-red);
      margin-left: 4rpx;
      font-weight: var(--font-weight-bold);
    }
  }
}

.form-input {
  width: 100%;
  height: 88rpx;
  background-color: var(--bg-input);
  border-radius: var(--radius);
  padding: 0 var(--spacing-14);
  font-size: var(--font-size-base);
  color: var(--text-base);
  box-sizing: border-box;
}

.form-textarea {
  width: 100%;
  background-color: var(--bg-input);
  border-radius: var(--radius);
  padding: var(--spacing-14);
  font-size: var(--font-size-base);
  color: var(--text-base);
  box-sizing: border-box;
  min-height: 200rpx;
  line-height: 1.5;
}

.picker-view {
  width: 100%;
  height: 88rpx;
  background-color: var(--bg-input);
  border-radius: var(--radius);
  padding: 0 var(--spacing-14);
  font-size: var(--font-size-base);
  color: var(--text-base);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-12);
}

.radio-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-10) var(--spacing-14);
  border-radius: var(--radius);
  background-color: var(--bg-input);
  transition: all 0.2s;
  font-size: var(--font-size-base);

  &.active {
    background-color: var(--bg-primary-light);
    color: var(--primary);
  }
}

.radio-icon {
  margin-right: var(--spacing-6);
  font-size: var(--font-size-md);
}

.skills-edit {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-12);
  margin-bottom: var(--spacing-12);
}

.skill-tag-edit {
  background-color: var(--bg-primary-light);
  color: var(--primary);
  padding: var(--spacing-8) var(--spacing-14);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  display: flex;
  align-items: center;
}

.delete-icon {
  margin-left: var(--spacing-6);
  color: var(--text-grey);
  font-size: var(--font-size-xs);
  width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background-color: rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-skill {
  display: flex;
  align-items: center;
  padding: var(--spacing-8) var(--spacing-14);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  color: var(--text-grey);
  background-color: var(--bg-input);
  border: 1rpx dashed var(--text-grey);
}

.skill-actions {
  margin-top: var(--spacing-10);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-12);
}

.cancel-btn,
.confirm-btn {
  padding: var(--spacing-8) var(--spacing-16);
  border-radius: var(--radius);
  font-size: var(--font-size-sm);
}

.cancel-btn {
  background-color: var(--bg-input);
  color: var(--text-secondary);
}

.confirm-btn {
  background-color: var(--primary);
  color: var(--text-inverse);
}

.work-items {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-12);
}

.work-item-edit {
  position: relative;
  width: 100%;
}

.delete-btn {
  position: absolute;
  right: var(--spacing-10);
  top: var(--spacing-10);
  width: 44rpx;
  height: 44rpx;
  border-radius: 22rpx;
  background-color: rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-grey);
  font-size: var(--font-size-md);
}

.add-work-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-12) 0;
  background-color: var(--bg-primary-light);
  border: 1rpx dashed rgba(var(--primary), 0.3);
  color: var(--primary);
  border-radius: var(--radius);
  font-size: var(--font-size-base);
  margin-top: var(--spacing-12);

  text:first-child {
    margin-right: var(--spacing-6);
  }
}

// 底部按钮
.bottom-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-12);
  background-color: var(--bg-card);
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding-bottom: calc(var(--spacing-12) + env(safe-area-inset-bottom));
  z-index: 10;
  width: 100%;
  box-sizing: border-box;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 44rpx;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  transition: all 0.2s;

  &:active {
    transform: scale(0.98);
  }
}

.outline-btn {
  margin-right: var(--spacing-20);
  border: 1rpx solid var(--primary);
  color: var(--primary);
  background-color: var(--bg-primary-light);

  &:active {
    background-color: rgba(var(--primary), 0.1);
  }
}

.primary-btn {
  background: linear-gradient(to right, var(--primary), var(--primary-500));
  color: var(--text-inverse);
  box-shadow: 0 4rpx 12rpx rgba(var(--primary), 0.3);

  &:active {
    box-shadow: 0 2rpx 8rpx rgba(var(--primary), 0.2);
  }
}

.time-range-picker {
  display: flex;
  align-items: center;
  gap: var(--spacing-12);
  width: 100%;
}

.time-picker-item {
  flex: 1;
  background-color: var(--bg-input);
  border-radius: var(--radius);
  padding: 0 var(--spacing-14);
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: var(--font-size-base);
  color: var(--text-base);
  position: relative;
  border: 1rpx solid transparent;
  transition: all 0.2s;

  &:active {
    border-color: var(--primary);
    background-color: rgba(var(--primary), 0.05);
  }

  text:first-child {
    flex: 1;
  }
}

.time-separator {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin: 0 var(--spacing-8);
  font-weight: var(--font-weight-medium);
}

.skill-count-hint {
  font-size: var(--font-size-xs);
  color: var(--text-info);
  margin-left: var(--spacing-6);
  font-weight: 400;
}

.skill-limit-tip {
  margin-top: var(--spacing-12);
  padding: var(--spacing-12) var(--spacing-14);
  background-color: rgba(var(--primary), 0.08);
  border-radius: var(--radius);
  font-size: var(--font-size-xs);
  color: var(--primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-8);
  border: 1rpx solid rgba(var(--primary), 0.2);

  text:first-child {
    font-size: var(--font-size-sm);
  }
}

.add-skill.disabled {
  opacity: 0.5;
  pointer-events: none;
  background-color: var(--bg-tag);
  color: var(--text-disable);
  border-color: var(--text-disable);
}

.active {
  color: var(--primary);
  font-weight: var(--font-weight-medium);
}

.bottom-placeholder {
  height: 120rpx;
}
</style>
