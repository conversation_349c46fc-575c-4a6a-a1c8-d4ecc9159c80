# 本地宝 CSS 样式指南

本指南详细介绍了项目的 CSS 样式系统，包括颜色变量、标签系统、字体系统、布局工具类等，帮助开发人员保持统一的设计风格。

## 目录

1. [颜色系统](#颜色系统)
2. [间距与圆角](#间距与圆角)
3. [字体系统](#字体系统)
4. [标签系统](#标签系统)
5. [布局工具类](#布局工具类)
6. [文本工具类](#文本工具类)
7. [背景与阴影](#背景与阴影)
8. [最佳实践](#最佳实践)

## 颜色系统

### 主题色

主题色分为不同亮度级别，从浅到深：

```scss
$primary-50: #fff9ec; // 最浅
$primary-100: #fff1d3;
$primary-200: #ffdfa5;
$primary-300: #ffc66d;
$primary-400: #ffa232;
$primary-500: #ff860a;
$primary-600: #ff6d00; // 默认主题色
$primary-700: #cc4e02;
$primary-800: #a13d0b;
$primary-900: #82340c;
$primary-950: #461804; // 最深
```

使用方式：

- `.text-primary` - 主题色文字
- `.bg-primary-light` - 主题色淡背景

### 文本颜色

```scss
$text-base: #212529; // 主要文本颜色，替代#333
$text-secondary: #52525b; // 次要文本颜色，替代#666
$text-info: #8f9098; // 描述文本颜色，用于标签
$text-grey: #a1a1aa; // 占位文本颜色，替代#999
$text-disable: #d4d4d8; // 禁用状态文本颜色
$text-inverse: #feffff; // 反色文本，用于深色背景
```

功能色：

```scss
$text-red: #dc2626; // 红色文本，警告和错误
$text-green: #22c55e; // 绿色文本，成功状态
$text-blue: #3b82f6; // 蓝色文本，链接和信息
$text-yellow: #eab308; // 黄色文本，警告
$text-purple: #8b5cf6; // 紫色文本
```

使用方式：

```html
<text class="text-base">主文本</text>
<text class="text-secondary">次要文本</text>
<text class="text-info">信息描述</text>
<text class="text-grey">灰色文本</text>
<text class="text-red">错误文本</text>
```

### 背景颜色

```scss
$bg-page: #f4f5fa; // 页面背景色
$bg-tag: #f2f2f7; // 标签背景色
$bg-card: #feffff; // 卡片背景色
$bg-search: #f1f2f7; // 搜索框背景色
$bg-mask: rgba(0, 0, 0, 0.5); // 遮罩背景色
```

功能背景色：

```scss
$primary-light: rgba(255, 109, 0, 0.1); // 主题色淡背景
$success-light: rgba(34, 197, 94, 0.1); // 成功色淡背景
$warning-light: rgba(234, 179, 8, 0.1); // 警告色淡背景
$danger-light: rgba(220, 38, 38, 0.1); // 危险色淡背景
$info-light: rgba(59, 130, 246, 0.1); // 信息色淡背景
```

使用方式：

```html
<view class="bg-card">卡片背景</view>
<view class="bg-primary-light">主题色淡背景</view>
```

## 间距与圆角

### 间距尺寸

```scss
$spacing-4: 8rpx; // 4px
$spacing-6: 12rpx; // 6px
$spacing-8: 16rpx; // 8px
$spacing-10: 20rpx; // 10px
$spacing-12: 24rpx; // 12px
$spacing-14: 28rpx; // 14px
$spacing-16: 32rpx; // 16px
$spacing-18: 36rpx; // 18px
$spacing-20: 40rpx; // 20px
$spacing-24: 48rpx; // 24px
$spacing-28: 56rpx; // 28px
$spacing-32: 64rpx; // 32px
$spacing-36: 72rpx; // 36px
$spacing-40: 80rpx; // 40px
```

### 圆角尺寸

```scss
$radius-sm: 8rpx; // 4px
$radius: 16rpx; // 8px
$radius-lg: 24rpx; // 12px
$radius-xl: 32rpx; // 16px
$radius-xxl: 40rpx; // 20px
```

## 字体系统

### 字体大小

```scss
$font-size-xs: 24rpx; // 12px
$font-size-sm: 26rpx; // 13px
$font-size-base: 28rpx; // 14px
$font-size-md: 30rpx; // 15px
$font-size-lg: 32rpx; // 16px
$font-size-xl: 36rpx; // 18px
$font-size-xxl: 40rpx; // 20px
```

使用方式：

```html
<text class="font-xs">小字</text>
<text class="font-base">基础字号</text>
<text class="font-lg">大字</text>
```

### 字体粗细

```scss
$font-weight-light: 300;
$font-weight-regular: 400;
$font-weight-medium: 500;
$font-weight-bold: 700;
```

使用方式：

```html
<text class="font-regular">常规字重</text>
<text class="font-medium">中等字重</text>
<text class="font-bold">粗体字重</text>
```

## 标签系统

标签是系统中频繁使用的 UI 元素，我们提供了多种样式变体：

### 基础标签

```html
<text class="tag">基础标签</text>
```

### 尺寸变体

```html
<text class="tag tag-sm">小标签</text>
<text class="tag">默认标签</text>
<text class="tag tag-lg">大标签</text>
```

### 颜色变体

```html
<text class="tag tag-primary">主题标签</text>
<text class="tag tag-success">成功标签</text>
<text class="tag tag-warning">警告标签</text>
<text class="tag tag-danger">危险标签</text>
<text class="tag tag-info">信息标签</text>
```

### 边框变体

```html
<text class="tag tag-outlined">边框标签</text>
<text class="tag tag-primary-outlined">主题边框标签</text>
<text class="tag tag-danger-outlined">危险边框标签</text>
```

### 标签容器

使用 `tag-container` 类包裹多个标签：

```html
<view class="tag-container">
  <text class="tag">标签1</text>
  <text class="tag">标签2</text>
  <text class="tag">标签3</text>
</view>
```

## 布局工具类

### Flex 布局

```html
<!-- 基础Flex布局 -->
<view class="flex">Flex容器</view>

<!-- 垂直方向Flex -->
<view class="flex flex-col">垂直Flex容器</view>

<!-- Flex水平垂直居中 -->
<view class="flex flex-center">居中内容</view>

<!-- 两端对齐 -->
<view class="flex flex-between">
  <text>左侧</text>
  <text>右侧</text>
</view>

<!-- 垂直居中 -->
<view class="flex flex-items-center">垂直居中的内容</view>

<!-- 占满剩余空间 -->
<view class="flex">
  <view>固定宽度</view>
  <view class="flex-1">占满剩余空间</view>
</view>
```

### 间距工具类

在 uni.scss 中声明的间距变量可通过 UnoCSS 工具类直接使用：

```html
<view class="p-4">内边距8rpx（4px）</view>
<view class="px-4">水平内边距8rpx</view>
<view class="py-4">垂直内边距8rpx</view>
<view class="pt-4">顶部内边距8rpx</view>
<view class="mb-8">底部外边距16rpx</view>
<view class="mx-auto">水平居中</view>
<view class="gap-4">栅格间距8rpx</view>
```

## 文本工具类

### 文本溢出

```html
<!-- 单行溢出省略 -->
<text class="text-line-1">单行文本溢出显示省略号</text>

<!-- 两行溢出省略 -->
<text class="text-line-2">两行文本溢出显示省略号，多余的内容将被截断</text>

<!-- 三行溢出省略 -->
<text class="text-line-3">三行文本溢出显示省略号，多余的内容将被截断</text>
```

### 文本对齐

```html
<view class="text-center">居中文本</view>
<view class="text-right">右对齐文本</view>
```

## 背景与阴影

### 阴影效果

```html
<view class="shadow">基础阴影</view>
<view class="shadow-md">中等阴影</view>
<view class="shadow-lg">大型阴影</view>
```

## 最佳实践

### 页面布局

推荐的页面基本结构：

```html
<template>
  <view class="container">
    <!-- 页面内容 -->
    <view class="content-card bg-card rounded-md p-16rpx mb-16rpx">
      <view class="card-title font-lg font-bold mb-12rpx">标题</view>
      <view class="card-content">
        <!-- 内容 -->
      </view>
    </view>
  </view>
</template>
```

### 颜色使用规范

- 主题色：用于主要按钮、重要信息高亮、品牌标识
- 文字层次：
  - `text-base` - 主要内容
  - `text-secondary` - 次要说明文字
  - `text-info` - 辅助描述信息
  - `text-grey` - 最轻量级文本，如时间、占位符

### 标签使用建议

- 使用 `tag` 类替换所有 `tag-item` 类
- 根据语义选择合适的颜色变体
- 小型标签尽量使用 `tag-sm` 减少占用空间

### 避免硬编码

避免直接在样式中使用颜色代码，始终使用变量：

```scss
// 不推荐 ❌
.custom-text {
  color: #333;
  background-color: #f5f5f5;
}

// 推荐 ✅
.custom-text {
  color: $text-base;
  background-color: $bg-tag;
}
```

### 组件改造步骤

1. 替换颜色硬编码为变量
2. 统一标签样式为 `tag` 系列类名
3. 使用字体变量替代硬编码字体大小
4. 使用间距变量替代硬编码间距
5. 利用现有工具类减少自定义 CSS
