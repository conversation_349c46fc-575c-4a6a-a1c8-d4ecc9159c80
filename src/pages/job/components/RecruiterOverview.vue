<template>
  <view class="recruiter-overview">
    <view class="overview-card">
      <view class="overview-title">
        <text class="i-carbon-chart-line mr-10rpx"></text>
        <text>今日数据</text>
      </view>
      <view class="overview-stats">
        <view class="stat-item" @tap="navigateToViews">
          <text class="stat-value">{{ stats.todayViews }}</text>
          <text class="stat-label">职位浏览</text>
        </view>
        <view class="stat-item" @tap="navigateToResumes">
          <text class="stat-value">{{ stats.todayResumes }}</text>
          <text class="stat-label">收到简历</text>
        </view>
        <view class="stat-item" @tap="navigateToJobs">
          <text class="stat-value">{{ stats.activeJobs }}</text>
          <text class="stat-label">在招职位</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
interface RecruiterStats {
  todayViews: number;
  todayResumes: number;
  activeJobs: number;
}

interface Props {
  stats: RecruiterStats;
}

const props = defineProps<Props>();

const navigateToViews = () => {
  uni.navigateTo({
    url: "/pages/job/analytics?type=views",
  });
};

const navigateToResumes = () => {
  uni.navigateTo({
    url: "/pages/job/resume-pool",
  });
};

const navigateToJobs = () => {
  uni.navigateTo({
    url: "/pages/job/manage",
  });
};
</script>

<style lang="scss" scoped>
.recruiter-overview {
  padding: 20rpx;

  .overview-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20rpx;
    padding: 32rpx;
    color: #fff;

    .overview-title {
      display: flex;
      align-items: center;
      font-size: 32rpx;
      font-weight: 600;
      margin-bottom: 24rpx;
    }

    .overview-stats {
      display: flex;
      justify-content: space-between;

      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 16rpx;
        border-radius: 12rpx;
        transition: all 0.2s;

        &:active {
          background-color: rgba(255, 255, 255, 0.1);
          transform: scale(0.95);
        }

        .stat-value {
          font-size: 48rpx;
          font-weight: 700;
          margin-bottom: 8rpx;
        }

        .stat-label {
          font-size: 24rpx;
          opacity: 0.9;
        }
      }
    }
  }
}
</style>
