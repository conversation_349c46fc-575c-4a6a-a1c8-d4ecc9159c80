<template>
  <view class="container">
    <uni-nav-bar
      :fixed="true"
      :border="false"
      status-bar
      title="修改简历"
      left-icon="left"
      @clickLeft="goBack"
    />

    <scroll-view scroll-y class="form-content">
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">修改个人信息</text>
        </view>

        <!-- Avatar -->
        <view class="form-item avatar-item">
          <text class="item-label">真实头像更能吸引老板</text>
          <view class="avatar-wrapper">
            <button
              class="avatar-button"
              open-type="chooseAvatar"
              @chooseavatar="onChooseAvatar"
            >
              <image
                :src="resumeData.avatar || '/static/images/avatar-male.png'"
                class="avatar-image"
                mode="aspectFill"
              />
              <view class="avatar-mask">
                <text class="i-carbon-camera camera-icon"></text>
                <text class="camera-text">上传头像</text>
              </view>
            </button>
          </view>
        </view>

        <!-- Name -->
        <view class="form-item">
          <text class="item-label">姓名</text>
          <view class="item-content">
            <input
              v-model="resumeData.name"
              class="item-input"
              placeholder="请输入姓名"
              placeholder-class="input-placeholder"
            />
          </view>
        </view>

        <!-- WeChat -->
        <view class="form-item">
          <text class="item-label">微信号</text>
          <view class="item-content">
            <input
              v-model="resumeData.wechat"
              class="item-input"
              placeholder="请输入微信号"
              placeholder-class="input-placeholder"
            />
          </view>
        </view>

        <!-- Gender -->
        <view class="form-item">
          <text class="item-label">性别</text>
          <view class="item-content">
            <view class="gender-switcher">
              <view
                class="gender-option"
                :class="{ active: resumeData.gender === '男' }"
                @tap="resumeData.gender = '男'"
              >
                男
              </view>
              <view
                class="gender-option"
                :class="{ active: resumeData.gender === '女' }"
                @tap="resumeData.gender = '女'"
              >
                女
              </view>
            </view>
          </view>
        </view>

        <!-- Birth Year (Age) -->
        <picker
          mode="date"
          fields="year"
          :value="resumeData.birthYear"
          @change="onBirthYearChange"
        >
          <view class="form-item">
            <text class="item-label">年龄</text>
            <view class="item-content">
              <text :class="{ placeholder: !resumeData.birthYear }">{{
                resumeData.birthYear || "请选择出生年份"
              }}</text>
              <text class="i-carbon-chevron-right action-icon"></text>
            </view>
          </view>
        </picker>

        <!-- Education -->
        <picker
          :range="educationOptions"
          :value="educationIndex"
          @change="onEducationChange"
        >
          <view class="form-item">
            <text class="item-label">学历</text>
            <view class="item-content">
              <text :class="{ placeholder: !resumeData.education }">{{
                resumeData.education || "请选择学历"
              }}</text>
              <text class="i-carbon-chevron-right action-icon"></text>
            </view>
          </view>
        </picker>

        <!-- Work Experience -->
        <picker
          :range="experienceOptions"
          :value="experienceIndex"
          @change="onExperienceChange"
        >
          <view class="form-item">
            <text class="item-label">工作时间</text>
            <view class="item-content">
              <text :class="{ placeholder: !resumeData.experience }">{{
                resumeData.experience || "请选择工作时间"
              }}</text>
              <text class="i-carbon-chevron-right action-icon"></text>
            </view>
          </view>
        </picker>
      </view>

      <view class="form-section">
        <!-- Expected Position -->
        <view class="form-item" @tap="navigateToJobCategory">
          <text class="item-label">期望职位</text>
          <view class="item-content">
            <text
              class="truncate-text"
              :class="{ placeholder: !resumeData.jobTitle }"
            >
              {{ resumeData.jobTitle || "请选择期望职位" }}
            </text>
            <text class="i-carbon-chevron-right action-icon"></text>
          </view>
        </view>

        <!-- Expected Salary -->
        <picker
          :range="salaryOptions"
          :value="salaryIndex"
          @change="onSalaryChange"
        >
          <view class="form-item">
            <text class="item-label">期望薪资</text>
            <view class="item-content">
              <text :class="{ placeholder: !resumeData.expectedSalary }">{{
                resumeData.expectedSalary || "请选择期望薪资"
              }}</text>
              <text class="i-carbon-chevron-right action-icon"></text>
            </view>
          </view>
        </picker>

        <!-- Job Location -->
        <view class="form-item" @tap="navigateToLocationPicker">
          <text class="item-label">求职区域</text>
          <view class="item-content">
            <text :class="{ placeholder: !resumeData.location }">{{
              resumeData.location || "请选择求职区域"
            }}</text>
            <text class="i-carbon-chevron-right action-icon"></text>
          </view>
        </view>
      </view>

      <view class="form-section">
        <!-- Phone Number -->
        <view class="form-item">
          <text class="item-label">手机号码</text>
          <view class="item-content">
            <text class="mr-2">{{ resumeData.phone }}</text>
            <view class="verification-tag">
              <text class="i-carbon-warning-alt text-red-500 text-sm"></text>
              <text class="text-red-500 text-xs ml-1">未验证</text>
            </view>
            <text class="i-carbon-chevron-right action-icon ml-auto"></text>
          </view>
        </view>
      </view>

      <!-- Personal Strengths -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">个人优势</text>
          <text class="section-hint">(选填)</text>
        </view>
        <view class="textarea-wrapper">
          <textarea
            v-model="resumeData.strengths"
            class="strengths-textarea"
            placeholder="填写你的个人优势，让老板更了解你"
            maxlength="500"
          />
        </view>
      </view>

      <!-- Work History -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-title">工作经历</text>
          <text class="section-hint">(选填)</text>
        </view>
        <view class="work-history-list">
          <view
            v-for="(exp, index) in resumeData.workHistory"
            :key="index"
            class="work-item"
            @tap="editWorkExperience(index)"
          >
            <view class="work-item-header">
              <text class="work-title">{{ exp.title }}</text>
              <text class="work-period"
                >{{ exp.startDate }} - {{ exp.endDate }}</text
              >
            </view>
            <view class="work-company">{{ exp.company }}</view>
          </view>
          <view class="add-work-btn" @tap="addWorkExperience">
            <text class="i-carbon-add-filled"></text>
            <text>添加工作经历</text>
          </view>
        </view>
      </view>

      <view class="bottom-placeholder"></view>
    </scroll-view>

    <view class="bottom-bar safe-area-inset-bottom">
      <button class="confirm-btn" @tap="saveResume">确认修改</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import {
  educationOptions,
  experienceOptions,
  salaryOptions,
} from "@/common/job";

const resumeData = reactive({
  avatar: "",
  name: "",
  wechat: "",
  gender: "男",
  birthYear: "",
  education: 0,
  experience: 0,
  jobTitle: "",
  expectedSalary: 0,
  location: "",
  phone: "13720020501", // Example phone
  strengths: "",
  workHistory: [],
});

// Mock data for demonstration
onLoad(() => {
  resumeData.name = "乔海申";
  resumeData.birthYear = "1994";
  resumeData.education = 3;
  resumeData.experience = 2;
  resumeData.jobTitle = "模具工/切割工/钳...";
  resumeData.expectedSalary = 2;
  resumeData.location = "桥梓/怀柔";
  resumeData.workHistory = [
    {
      company: "北京制造厂",
      title: "模具工程师",
      startDate: "2020.03",
      endDate: "至今",
      description: "负责模具设计与维护工作",
    },
  ];
});

const educationIndex = computed(() =>
  educationOptions.findIndex((option) => option.value === resumeData.education)
);
const experienceIndex = computed(() =>
  experienceOptions.findIndex(
    (option) => option.value === resumeData.experience
  )
);
const salaryIndex = computed(() =>
  salaryOptions.findIndex(
    (option) => option.value === resumeData.expectedSalary
  )
);

const goBack = () => uni.navigateBack();

// 头像选择 - 使用微信原生组件
const onChooseAvatar = (e: any) => {
  const { avatarUrl } = e.detail;
  resumeData.avatar = avatarUrl;
};

// 备用头像选择方法（兼容性）
const chooseAvatar = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ["compressed"],
    sourceType: ["album", "camera"],
    success: (res) => {
      // @ts-ignore
      resumeData.avatar = res.tempFilePaths[0];
    },
  });
};

const onBirthYearChange = (e: any) => {
  resumeData.birthYear = e.detail.value;
};

const onEducationChange = (e: any) => {
  resumeData.education = educationOptions[e.detail.value].value;
};

const onExperienceChange = (e: any) => {
  resumeData.experience = experienceOptions[e.detail.value].value;
};

const onSalaryChange = (e: any) => {
  resumeData.expectedSalary = salaryOptions[e.detail.value].value;
};

const navigateToJobCategory = () => {
  // Navigate to job category selection page
  uni.navigateTo({ url: "/pages/job/category/index" }); // Placeholder URL
};

const navigateToLocationPicker = () => {
  // Navigate to location picker page
  uni.navigateTo({ url: "/pages/common/locationPicker" }); // Placeholder URL
};

const addWorkExperience = () => {
  uni.navigateTo({
    url: "/pages/job/resume/edit?section=experience&action=add",
  });
};

const editWorkExperience = (index: number) => {
  uni.navigateTo({
    url: `/pages/job/resume/edit?section=experience&action=edit&index=${index}`,
  });
};

const saveResume = () => {
  console.log("Saving resume:", resumeData);
  uni.showToast({
    title: "修改成功",
    icon: "success",
  });
  setTimeout(() => goBack(), 1500);
};
</script>

<style lang="scss" scoped>
.container {
  .form-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 180rpx;
  }

  .form-section {
    background-color: var(--bg-card);
    border-radius: var(--radius-lg);
    margin: var(--spacing-12);
    padding: 0 var(--spacing-16);
  }

  .section-header {
    display: flex;
    align-items: center;
    padding: var(--spacing-16) 0;
    .section-title {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-bold);
      color: var(--text-base);
    }
    .section-hint {
      font-size: var(--font-size-sm);
      color: var(--text-info);
      margin-left: var(--spacing-8);
    }
  }

  .form-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 100rpx;
    border-bottom: 1rpx solid var(--border-color);

    &:last-child {
      border-bottom: none;
    }

    &.avatar-item {
      min-height: 140rpx;
      flex-direction: column;
      align-items: flex-start;
    }
  }

  .item-label {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-6);
  }

  .item-content {
    display: flex;
    align-items: center;
    color: var(--text-base);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);

    .item-input {
      text-align: right;
      width: 100%;
      font-weight: var(--font-weight-medium);
    }

    .input-placeholder {
      color: var(--text-info);
      font-weight: var(--font-weight-regular);
    }

    .placeholder {
      color: var(--text-info);
      font-weight: var(--font-weight-regular);
    }
  }

  // 头像相关样式
  .avatar-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: var(--spacing-8);
  }

  .avatar-button {
    position: relative;
    padding: 0;
    border: none;
    background: transparent;
    border-radius: 50%;
    overflow: hidden;

    &::after {
      border: none;
    }
  }

  .avatar-image {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    background-color: var(--bg-tag);
    border: 2rpx solid var(--border-color);
  }

  .avatar-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease;

    .camera-icon {
      font-size: var(--font-size-lg);
      color: var(--text-inverse);
      margin-bottom: var(--spacing-4);
    }

    .camera-text {
      font-size: var(--font-size-xs);
      color: var(--text-inverse);
    }
  }

  .avatar-button:active .avatar-mask {
    opacity: 1;
  }

  .action-icon {
    color: var(--text-grey);
    font-size: var(--font-size-lg);
  }

  .gender-switcher {
    display: flex;
    border: 2rpx solid var(--border-color);
    border-radius: var(--radius-xl);
    overflow: hidden;

    .gender-option {
      padding: var(--spacing-6) var(--spacing-20);
      background-color: var(--bg-card);
      color: var(--text-secondary);
      transition: all 0.2s ease;
      &.active {
        background-color: var(--bg-primary-light);
        color: var(--primary);
        font-weight: var(--font-weight-bold);
      }
    }
  }

  .truncate-text {
    max-width: 400rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: right;
  }

  .verification-tag {
    display: inline-flex;
    align-items: center;
    background-color: var(--bg-danger-light);
    padding: var(--spacing-4) var(--spacing-6);
    border-radius: var(--radius-sm);
  }

  .textarea-wrapper {
    padding-bottom: var(--spacing-16);
  }

  .strengths-textarea {
    width: 100%;
    height: 200rpx;
    background-color: var(--bg-input);
    border-radius: var(--radius);
    padding: var(--spacing-12);
    font-size: var(--font-size-base);
    color: var(--text-base);
    box-sizing: border-box;
  }

  .work-history-list {
    padding-bottom: var(--spacing-8);
  }

  .work-item {
    padding: var(--spacing-12) 0;
    border-bottom: 1rpx solid var(--border-color);

    .work-item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-4);
    }

    .work-title {
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-bold);
      color: var(--text-base);
    }
    .work-period {
      font-size: var(--font-size-sm);
      color: var(--text-info);
    }
    .work-company {
      font-size: var(--font-size-base);
      color: var(--text-secondary);
    }
  }

  .add-work-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 90rpx;
    background-color: var(--bg-primary-light);
    color: var(--primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    border-radius: var(--radius);

    text {
      margin: 0 var(--spacing-4);
    }
  }

  .bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--bg-card);
    padding: var(--spacing-12);
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  }

  .confirm-btn {
    background: linear-gradient(90deg, var(--primary-400), var(--primary));
    color: var(--text-inverse);
    border-radius: var(--radius-xxl);
    height: 96rpx;
    line-height: 96rpx;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    border: none;
    &:active {
      opacity: 0.9;
    }
  }

  .bottom-placeholder {
    height: var(--spacing-10);
  }
}
</style>
