<template>
  <view class="conversation-page">
    <!-- 顶部导航栏 -->
    <tui-navigation-bar :isFixed="true" :alpha="0" backgroundColor="#fff">
      <template #default>
        <view class="nav-title">
          <text>{{ title }}</text>
          <text class="nav-subtitle" v-if="contactInfo.online">在线</text>
        </view>
      </template>
      <template #left>
        <view class="nav-left" @tap="goBack">
          <tui-icon name="arrowleft" size="20" color="#333"></tui-icon>
        </view>
      </template>
      <template #right>
        <view class="nav-right" @tap="showMore">
          <tui-icon name="more-fill" size="20" color="#333"></tui-icon>
        </view>
      </template>
    </tui-navigation-bar>

    <!-- 消息列表区域 -->
    <scroll-view
      class="message-scroll"
      scroll-y
      :scroll-top="scrollTop"
      @scrolltoupper="loadMoreMessages"
      upper-threshold="50"
    >
      <view class="messages-container">
        <!-- 时间分隔 -->
        <view class="time-divider">
          <text>{{ currentTime }}</text>
        </view>

        <!-- 消息列表 -->
        <block v-for="(msg, index) in messages" :key="index">
          <!-- 时间提示 -->
          <view class="time-tip" v-if="msg.showTime">
            <text>{{ msg.time }}</text>
          </view>

          <!-- 消息气泡 -->
          <view class="message-item" :class="{ 'message-self': msg.isSelf }">
            <!-- 非自己的消息显示头像 -->
            <image
              v-if="!msg.isSelf"
              class="avatar"
              :src="contactInfo.avatar"
              mode="aspectFill"
            ></image>

            <!-- 消息内容 -->
            <view class="message-bubble" :class="{ 'self-bubble': msg.isSelf }">
              <!-- 文本消息 -->
              <text v-if="msg.type === 'text'" class="message-text">{{
                msg.content
              }}</text>

              <!-- 图片消息 -->
              <image
                v-if="msg.type === 'image'"
                class="message-image"
                :src="msg.content"
                mode="widthFix"
                @tap="previewImage(msg.content)"
              ></image>

              <!-- 视频消息 -->
              <view
                v-if="msg.type === 'video'"
                class="message-video"
                @tap="playVideo(msg.content)"
              >
                <image
                  class="video-cover"
                  :src="msg.cover"
                  mode="aspectFill"
                ></image>
                <view class="video-play-icon">
                  <tui-icon name="play-fill" color="#fff" size="24"></tui-icon>
                </view>
              </view>

              <!-- 语音消息 -->
              <view
                v-if="msg.type === 'audio'"
                class="message-audio"
                @tap="playAudio(msg.content)"
              >
                <tui-icon
                  name="sound"
                  :color="msg.isSelf ? '#fff' : '#333'"
                  size="20"
                ></tui-icon>
                <text :class="{ 'self-text': msg.isSelf }"
                  >{{ msg.duration }}"</text
                >
              </view>

              <!-- 位置消息 -->
              <view
                v-if="msg.type === 'location'"
                class="message-location"
                @tap="openLocation(msg.location)"
              >
                <image
                  class="location-image"
                  :src="msg.locationImage"
                  mode="aspectFill"
                ></image>
                <view class="location-info">
                  <text class="location-name">{{ msg.locationName }}</text>
                  <text class="location-address">{{
                    msg.locationAddress
                  }}</text>
                </view>
              </view>
            </view>

            <!-- 自己的消息显示头像 -->
            <image
              v-if="msg.isSelf"
              class="avatar"
              :src="userInfo.avatar"
              mode="aspectFill"
            ></image>
          </view>
        </block>

        <!-- 对方正在输入提示 -->
        <view class="typing-indicator" v-if="isTyping">
          <view class="typing-avatar">
            <image
              class="avatar"
              :src="contactInfo.avatar"
              mode="aspectFill"
            ></image>
          </view>
          <view class="typing-bubble">
            <view class="typing-dots">
              <view class="typing-dot"></view>
              <view class="typing-dot"></view>
              <view class="typing-dot"></view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部输入区域 -->
    <view class="input-area">
      <view class="input-toolbar">
        <view class="voice-btn" @tap="toggleVoiceInput">
          <tui-icon
            :name="isVoiceMode ? 'keyboard' : 'sound'"
            size="24"
            color="#333"
          ></tui-icon>
        </view>

        <!-- 文本输入 -->
        <view class="input-box" v-if="!isVoiceMode">
          <input
            class="text-input"
            type="text"
            v-model="inputMessage"
            confirm-type="send"
            placeholder="请输入消息..."
            @confirm="sendTextMessage"
            @focus="inputFocus"
            @blur="inputBlur"
          />
        </view>

        <!-- 语音输入 -->
        <view
          class="voice-input-box"
          v-else
          @touchstart="startRecording"
          @touchend="endRecording"
          @touchcancel="cancelRecording"
        >
          <text>按住说话</text>
        </view>

        <view class="emoji-btn" @tap="toggleEmoji">
          <tui-icon name="emoji" size="24" color="#333"></tui-icon>
        </view>

        <view class="more-btn" @tap="toggleMore">
          <tui-icon name="add" size="24" color="#333"></tui-icon>
        </view>
      </view>

      <!-- 表情面板 -->
      <view class="emoji-panel" v-if="showEmojiPanel">
        <scroll-view scroll-y class="emoji-scroll">
          <view class="emoji-list">
            <view
              class="emoji-item"
              v-for="(emoji, index) in emojiList"
              :key="index"
              @tap="insertEmoji(emoji)"
            >
              {{ emoji }}
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 更多功能面板 -->
      <view class="more-panel" v-if="showMorePanel">
        <view class="more-panel-row">
          <view class="more-item" @tap="chooseImage">
            <view class="more-item-icon">
              <tui-icon name="picture" size="24" color="#fff"></tui-icon>
            </view>
            <text>照片</text>
          </view>
          <view class="more-item" @tap="takePhoto">
            <view class="more-item-icon camera">
              <tui-icon name="camera" size="24" color="#fff"></tui-icon>
            </view>
            <text>拍摄</text>
          </view>
          <view class="more-item" @tap="chooseLocation">
            <view class="more-item-icon location">
              <tui-icon name="position" size="24" color="#fff"></tui-icon>
            </view>
            <text>位置</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 录音提示 -->
    <view class="recording-mask" v-if="isRecording">
      <view class="recording-box">
        <view class="recording-icon">
          <tui-icon name="sound-filling" size="40" color="#ff6d00"></tui-icon>
        </view>
        <text>手指上滑，取消发送</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";

// 定义消息类型接口
interface MessageBase {
  id: string;
  content: string;
  type: "text" | "image" | "video" | "audio" | "location";
  isSelf: boolean;
  time: string;
  showTime: boolean;
}

interface TextMessage extends MessageBase {
  type: "text";
  status?: "sending" | "sent" | "fail" | "read";
}

interface ImageMessage extends MessageBase {
  type: "image";
}

interface VideoMessage extends MessageBase {
  type: "video";
  cover: string;
}

interface AudioMessage extends MessageBase {
  type: "audio";
  duration: string;
}

interface LocationMessage extends MessageBase {
  type: "location";
  locationName: string;
  locationAddress: string;
  locationImage: string;
  location: {
    latitude: number;
    longitude: number;
    name: string;
    address: string;
  };
}

type Message =
  | TextMessage
  | ImageMessage
  | VideoMessage
  | AudioMessage
  | LocationMessage;

// 页面参数
const title = ref("");
const contactId = ref("");

// 页面加载
onLoad((options: any) => {
  if (options.id) {
    contactId.value = options.id;
  }
  if (options.name) {
    title.value = options.name;
  }

  // 加载聊天数据
  loadChatHistory();
});

// 联系人信息
const contactInfo = ref({
  id: "",
  name: "",
  avatar: "/static/images/avatar1.png",
  online: true,
});

// 用户信息
const userInfo = ref({
  id: "self",
  name: "我",
  avatar: "/static/images/user.png",
});

// 消息列表
const messages = ref<Message[]>([
  {
    id: "1",
    content: "你好，我对您发的设计很感兴趣",
    type: "text",
    isSelf: true,
    time: "10:01",
    showTime: true,
    status: "read", // 'sending', 'sent', 'fail', 'read'
  },
  {
    id: "2",
    content: "您好！很高兴收到您的消息，有什么可以帮到您的吗？",
    type: "text",
    isSelf: false,
    time: "10:02",
    showTime: false,
  },
  {
    id: "3",
    content: "我想了解一下您的设计方案",
    type: "text",
    isSelf: true,
    time: "10:05",
    showTime: true,
  },
  {
    id: "4",
    content: "/static/images/design.jpg",
    type: "image",
    isSelf: false,
    time: "10:06",
    showTime: false,
  },
  {
    id: "5",
    content: "这是我们的最新设计案例",
    type: "text",
    isSelf: false,
    time: "10:06",
    showTime: false,
  },
  {
    id: "6",
    content: "看起来不错，价格怎么样？",
    type: "text",
    isSelf: true,
    time: "10:08",
    showTime: true,
  },
]);

// 输入相关
const inputMessage = ref("");
const isVoiceMode = ref(false);
const showEmojiPanel = ref(false);
const showMorePanel = ref(false);
const isRecording = ref(false);
const isTyping = ref(false);
const scrollTop = ref(0);

// 表情列表
const emojiList = ref([
  "😀",
  "😁",
  "😂",
  "🤣",
  "😃",
  "😄",
  "😅",
  "😆",
  "😉",
  "😊",
  "😋",
  "😎",
  "😍",
  "😘",
  "😗",
  "😙",
  "😚",
  "🙂",
  "🤗",
  "🤩",
  "🤔",
  "🤨",
  "😐",
  "😑",
  "😶",
  "🙄",
  "😏",
  "😣",
  "😥",
  "😮",
  "🤐",
  "😯",
  "😪",
  "😫",
  "😴",
  "😌",
  "😛",
  "😜",
  "😝",
  "🤤",
  "😒",
  "😓",
  "😔",
  "😕",
  "🙃",
  "🤑",
  "😲",
  "☹️",
  "🙁",
  "😖",
  "😞",
  "😟",
  "😤",
  "😢",
  "😭",
  "😦",
  "😧",
  "😨",
  "😩",
  "🤯",
  "😬",
  "😰",
  "😱",
  "😳",
  "🤪",
  "😵",
  "😡",
  "😠",
  "🤬",
  "😷",
  "🤒",
  "🤕",
  "🤢",
  "🤮",
  "🤧",
  "😇",
  "🤠",
  "🤡",
  "🤥",
  "🤫",
  "🤭",
]);

// 当前时间
const currentTime = computed(() => {
  const now = new Date();
  const hours = now.getHours().toString().padStart(2, "0");
  const minutes = now.getMinutes().toString().padStart(2, "0");
  return `${hours}:${minutes}`;
});

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 显示更多操作
const showMore = () => {
  uni.showActionSheet({
    itemList: ["清空聊天记录", "举报", "拉黑"],
    success: (res) => {
      if (res.tapIndex === 0) {
        uni.showModal({
          title: "提示",
          content: "确定清空聊天记录吗？",
          success: (res) => {
            if (res.confirm) {
              messages.value = [];
            }
          },
        });
      }
    },
  });
};

// 加载历史聊天记录
const loadChatHistory = () => {
  // 实际开发中应该从服务器或本地存储加载
  console.log("加载聊天记录");
};

// 加载更多历史消息
const loadMoreMessages = () => {
  // 实际开发中应该加载更早的消息
  console.log("加载更多历史消息");
};

// 发送文本消息
const sendTextMessage = () => {
  if (!inputMessage.value.trim()) return;

  const newMessage: TextMessage = {
    id: Date.now().toString(),
    content: inputMessage.value,
    type: "text",
    isSelf: true,
    time: currentTime.value,
    showTime: true,
    status: "sending",
  };

  messages.value.push(newMessage);
  inputMessage.value = "";
  scrollToBottom();

  // 模拟发送成功
  setTimeout(() => {
    const index = messages.value.findIndex((m) => m.id === newMessage.id);
    if (index !== -1 && messages.value[index].type === "text") {
      (messages.value[index] as TextMessage).status = "sent";
    }

    // 模拟对方回复
    setTimeout(() => {
      isTyping.value = true;
      scrollToBottom();

      setTimeout(() => {
        isTyping.value = false;
        const replyMessage: TextMessage = {
          id: Date.now().toString(),
          content: "好的，我明白了",
          type: "text",
          isSelf: false,
          time: currentTime.value,
          showTime: false,
        };
        messages.value.push(replyMessage);
        scrollToBottom();
      }, 2000);
    }, 1000);
  }, 500);
};

// 插入表情
const insertEmoji = (emoji: string) => {
  inputMessage.value += emoji;
};

// 切换语音输入模式
const toggleVoiceInput = () => {
  isVoiceMode.value = !isVoiceMode.value;
  showEmojiPanel.value = false;
  showMorePanel.value = false;
};

// 切换表情面板
const toggleEmoji = () => {
  showEmojiPanel.value = !showEmojiPanel.value;
  showMorePanel.value = false;
};

// 切换更多功能面板
const toggleMore = () => {
  showMorePanel.value = !showMorePanel.value;
  showEmojiPanel.value = false;
};

// 开始录音
const startRecording = () => {
  isRecording.value = true;
  // 实际开发中应调用录音API
};

// 结束录音发送
const endRecording = () => {
  isRecording.value = false;
  // 实际开发中应结束录音并发送

  const audioMessage: AudioMessage = {
    id: Date.now().toString(),
    content: "audio.mp3",
    type: "audio",
    isSelf: true,
    time: currentTime.value,
    showTime: true,
    duration: "15",
  };
  messages.value.push(audioMessage);
  scrollToBottom();
};

// 取消录音
const cancelRecording = () => {
  isRecording.value = false;
  // 实际开发中应取消录音
};

// 选择图片
const chooseImage = () => {
  uni.chooseImage({
    count: 1,
    success: (res) => {
      const tempFilePath = res.tempFilePaths[0];
      const imageMessage: ImageMessage = {
        id: Date.now().toString(),
        content: tempFilePath,
        type: "image",
        isSelf: true,
        time: currentTime.value,
        showTime: true,
      };
      messages.value.push(imageMessage);
      scrollToBottom();
      showMorePanel.value = false;
    },
  });
};

// 拍照
const takePhoto = () => {
  uni.chooseImage({
    count: 1,
    sourceType: ["camera"],
    success: (res) => {
      const tempFilePath = res.tempFilePaths[0];
      const imageMessage: ImageMessage = {
        id: Date.now().toString(),
        content: tempFilePath,
        type: "image",
        isSelf: true,
        time: currentTime.value,
        showTime: true,
      };
      messages.value.push(imageMessage);
      scrollToBottom();
      showMorePanel.value = false;
    },
  });
};

// 选择位置
const chooseLocation = () => {
  uni.chooseLocation({
    success: (res) => {
      const locationMessage: LocationMessage = {
        id: Date.now().toString(),
        content: res.name,
        type: "location",
        isSelf: true,
        time: currentTime.value,
        showTime: true,
        locationName: res.name,
        locationAddress: res.address,
        locationImage: "/static/images/map.png",
        location: {
          latitude: res.latitude,
          longitude: res.longitude,
          name: res.name,
          address: res.address,
        },
      };
      messages.value.push(locationMessage);
      scrollToBottom();
      showMorePanel.value = false;
    },
  });
};

// 预览图片
const previewImage = (url: string) => {
  const urls = messages.value
    .filter((msg) => msg.type === "image")
    .map((msg) => msg.content);
  uni.previewImage({
    current: url,
    urls: urls,
  });
};

// 播放视频
const playVideo = (url: string) => {
  uni.navigateTo({
    url: `/pages/message/video-player?url=${encodeURIComponent(url)}`,
  });
};

// 播放语音
const playAudio = (url: string) => {
  // 实际开发中应使用uni.createInnerAudioContext播放音频
  console.log("播放语音", url);
};

// 打开位置
const openLocation = (location: any) => {
  uni.openLocation({
    latitude: location.latitude,
    longitude: location.longitude,
    name: location.name,
    address: location.address,
  });
};

// 输入框获得焦点
const inputFocus = () => {
  showEmojiPanel.value = false;
  showMorePanel.value = false;
  scrollToBottom();
};

// 输入框失去焦点
const inputBlur = () => {
  // 可以处理失去焦点的逻辑
};

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    // 由于无法直接获取scroll-view的高度
    // 使用延时确保滚动到最新消息
    setTimeout(() => {
      const query = uni.createSelectorQuery();
      query.select(".messages-container").boundingClientRect();
      query.select(".message-scroll").boundingClientRect();
      query.exec((res) => {
        if (res[0] && res[1]) {
          const scrollHeight = res[0].height;
          const viewHeight = res[1].height;
          scrollTop.value = scrollHeight - viewHeight;
        }
      });
    }, 100);
  });
};

// 页面加载完成滚动到底部
onMounted(() => {
  scrollToBottom();
});
</script>

<style lang="scss" scoped>
.conversation-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.nav-subtitle {
  font-size: 20rpx;
  color: #07c160;
  margin-top: 2rpx;
}

.nav-left,
.nav-right {
  padding: 10rpx;
}

.message-scroll {
  flex: 1;
  padding: 20rpx;
}

.messages-container {
  padding-bottom: 20rpx;
}

.time-divider {
  text-align: center;
  margin: 20rpx 0;
}

.time-divider text {
  font-size: 22rpx;
  color: #999;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.time-tip {
  text-align: center;
  margin: 16rpx 0;
}

.time-tip text {
  font-size: 22rpx;
  color: #999;
}

.message-item {
  display: flex;
  margin-bottom: 24rpx;
  align-items: flex-start;
}

.message-self {
  flex-direction: row-reverse;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  background-color: #f1f1f1;
}

.message-bubble {
  max-width: 70%;
  margin: 0 20rpx;
  padding: 16rpx 24rpx;
  background-color: #fff;
  border-radius: 8rpx 24rpx 24rpx 24rpx;
  position: relative;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.self-bubble {
  background-color: #ff6d00;
  border-radius: 24rpx 8rpx 24rpx 24rpx;
}

.message-text {
  font-size: 28rpx;
  line-height: 1.5;
  word-break: break-word;
}

.self-bubble .message-text {
  color: #fff;
}

.message-image {
  max-width: 100%;
  border-radius: 8rpx;
}

.message-video {
  width: 300rpx;
  height: 200rpx;
  position: relative;
  border-radius: 8rpx;
  overflow: hidden;
}

.video-cover {
  width: 300rpx;
  height: 200rpx;
  border-radius: 8rpx;
}

.video-play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.5);
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-audio {
  display: flex;
  align-items: center;
  padding: 8rpx 0;
  min-width: 120rpx;
}

.message-audio text {
  margin-left: 12rpx;
  font-size: 26rpx;
}

.self-text {
  color: #fff;
}

.message-location {
  width: 300rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.location-image {
  width: 300rpx;
  height: 160rpx;
  border-radius: 8rpx 8rpx 0 0;
}

.location-info {
  background-color: #fff;
  padding: 12rpx;
}

.location-name {
  font-size: 26rpx;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.location-address {
  font-size: 22rpx;
  color: #999;
  margin-top: 4rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.typing-indicator {
  display: flex;
  margin-bottom: 24rpx;
}

.typing-avatar {
  margin-right: 20rpx;
}

.typing-bubble {
  padding: 20rpx;
  background-color: #fff;
  border-radius: 8rpx 24rpx 24rpx 24rpx;
}

.typing-dots {
  display: flex;
}

.typing-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 6rpx;
  background-color: #ccc;
  margin-right: 8rpx;
  animation: typing-dot 1s infinite;
}

.typing-dot:nth-child(1) {
  animation-delay: 0s;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
  margin-right: 0;
}

@keyframes typing-dot {
  0%,
  60%,
  100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10rpx);
  }
}

.input-area {
  background-color: #f1f1f1;
  border-top: 1px solid #e5e5e5;
  padding-bottom: env(safe-area-inset-bottom);
}

.input-toolbar {
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx;
}

.voice-btn,
.emoji-btn,
.more-btn {
  padding: 10rpx;
}

.input-box {
  flex: 1;
  background-color: #fff;
  border-radius: 8rpx;
  margin: 0 16rpx;
}

.text-input {
  width: 100%;
  height: 70rpx;
  font-size: 28rpx;
  padding: 0 20rpx;
}

.voice-input-box {
  flex: 1;
  height: 70rpx;
  background-color: #fff;
  border-radius: 8rpx;
  margin: 0 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.voice-input-box text {
  font-size: 28rpx;
  color: #666;
}

.emoji-panel,
.more-panel {
  height: 400rpx;
  background-color: #f8f8f8;
  border-top: 1px solid #e5e5e5;
  padding: 20rpx;
}

.emoji-scroll {
  height: 100%;
}

.emoji-list {
  display: flex;
  flex-wrap: wrap;
}

.emoji-item {
  width: 12.5%;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
}

.more-panel-row {
  display: flex;
  justify-content: flex-start;
}

.more-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 60rpx;
  margin-bottom: 40rpx;
}

.more-item-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 20rpx;
  background-color: #007aff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.camera {
  background-color: #5677fc;
}

.location {
  background-color: #2fd0ac;
}

.more-item text {
  font-size: 24rpx;
}

.recording-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.recording-box {
  width: 300rpx;
  height: 300rpx;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.recording-icon {
  margin-bottom: 30rpx;
}

.recording-box text {
  font-size: 26rpx;
  color: #fff;
}
</style>
