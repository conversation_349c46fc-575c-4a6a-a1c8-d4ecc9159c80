<template>
  <view class="tool-grid">
    <view class="grid" :class="gridClass">
      <view
        v-for="(tool, index) in tools"
        :key="index"
        class="tool-item"
        @tap="handleToolClick(tool)"
      >
        <view class="tool-icon-circle" :class="tool.bgColor">
          <text :class="tool.icon"></text>
        </view>
        <text class="tool-label">{{ tool.name }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
interface Tool {
  id: string;
  name: string;
  icon: string;
  bgColor: string;
}

interface Props {
  tools: Tool[];
  role: "jobseeker" | "recruiter";
}

interface Emits {
  (e: "tool-click", tool: Tool): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const gridClass = computed(() => {
  return props.role === "jobseeker" ? "grid-cols-5" : "grid-cols-4";
});

const handleToolClick = (tool: Tool) => {
  emit("tool-click", tool);
};
</script>

<style lang="scss" scoped>
.tool-grid {
  padding: 30rpx 20rpx;
  background-color: #fff;
  margin: 20rpx;
  border-radius: 20rpx;

  .grid {
    display: grid;
    gap: 32rpx;

    &.grid-cols-4 {
      grid-template-columns: repeat(4, 1fr);
    }

    &.grid-cols-5 {
      grid-template-columns: repeat(5, 1fr);
    }
  }

  .tool-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16rpx;
    transition: transform 0.2s;

    &:active {
      transform: scale(0.95);
    }
  }

  .tool-icon-circle {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 40rpx;
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);

    // 背景色类
    &.bg-primary {
      background: linear-gradient(135deg, var(--primary), var(--primary-400));
    }

    &.bg-success {
      background: linear-gradient(135deg, #10b981, #34d399);
    }

    &.bg-info {
      background: linear-gradient(135deg, #3b82f6, #60a5fa);
    }

    &.bg-warning {
      background: linear-gradient(135deg, #f59e0b, #fbbf24);
    }

    &.bg-purple {
      background: linear-gradient(135deg, #8b5cf6, #a78bfa);
    }

    &.bg-orange-500 {
      background: linear-gradient(135deg, #f97316, #fb923c);
    }

    &.bg-amber-500 {
      background: linear-gradient(135deg, #f59e0b, #fbbf24);
    }

    &.bg-emerald-500 {
      background: linear-gradient(135deg, #10b981, #34d399);
    }

    &.bg-blue-500 {
      background: linear-gradient(135deg, #3b82f6, #60a5fa);
    }

    &.bg-pink-500 {
      background: linear-gradient(135deg, #ec4899, #f472b6);
    }
  }

  .tool-label {
    font-size: 26rpx;
    color: var(--text-secondary);
    text-align: center;
    line-height: 1.2;
  }
}
</style>
