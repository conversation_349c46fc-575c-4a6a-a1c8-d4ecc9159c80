<template>
  <view class="role-switcher">
    <view class="role-switch-container">
      <view
        class="role-switch-btn"
        :class="{ active: currentRole === 'jobseeker' }"
        @tap="handleRoleSwitch('jobseeker')"
      >
        <text class="i-carbon-user mr-8rpx"></text>
        <text>我要找工作</text>
      </view>
      <view
        class="role-switch-btn"
        :class="{ active: currentRole === 'recruiter' }"
        @tap="handleRoleSwitch('recruiter')"
      >
        <text class="i-carbon-user-multiple mr-8rpx"></text>
        <text>我要招人</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
interface Props {
  currentRole: "jobseeker" | "recruiter";
}

interface Emits {
  (e: "role-change", role: "jobseeker" | "recruiter"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const handleRoleSwitch = (role: "jobseeker" | "recruiter") => {
  if (role !== props.currentRole) {
    emit("role-change", role);
  }
};
</script>

<style lang="scss" scoped>
.role-switcher {
  padding: 20rpx;

  .role-switch-container {
    display: flex;
    background-color: #fff;
    border-radius: 32rpx;
    padding: 8rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);

    .role-switch-btn {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 24rpx;
      border-radius: 24rpx;
      font-size: 28rpx;
      font-weight: 500;
      color: var(--text-secondary);
      transition: all 0.3s;

      &.active {
        background: linear-gradient(135deg, var(--primary), var(--primary-400));
        color: #fff;
        box-shadow: 0 4rpx 12rpx rgba(var(--primary-rgb), 0.3);
      }

      &:active {
        transform: scale(0.98);
      }
    }
  }
}
</style>
