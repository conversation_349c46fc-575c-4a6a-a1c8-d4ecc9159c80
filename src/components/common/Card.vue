<template>
  <view class="card-box" :class="{ 'box-shadow': isShadow }" :style="{ borderRadius: borderRadius, padding: padding, margin: margin }">
    <view v-if="title" class="card-header flex justify-between items-center mb-20rpx">
      <view class="flex items-center">
        <view v-if="showTitleLine" class="title-line mr-10rpx" :style="{ backgroundColor: lineColor }"></view>
        <text class="text-34rpx font-500" :style="{ color: titleColor }">{{ title }}</text>
      </view>
      <text v-if="subTitle" class="text-26rpx text-grey">{{ subTitle }}</text>
    </view>
    <view class="card-content">
      <slot></slot>
    </view>
  </view>
</template>

<script setup lang="ts">
defineProps({
  title: {
    type: String,
    default: ''
  },
  subTitle: {
    type: String,
    default: ''
  },
  borderRadius: {
    type: String,
    default: '20rpx'
  },
  padding: {
    type: String,
    default: '20rpx'
  },
  margin: {
    type: String,
    default: '20rpx'
  },
  showTitleLine: {
    type: Boolean,
    default: true
  },
  lineColor: {
    type: String,
    default: 'var(--primary)'
  },
  titleColor: {
    type: String,
    default: 'var(--text-base)'
  },
  isShadow: {
    type: Boolean,
    default: true
  }
})
</script>

<style lang="scss" scoped>
.card-box {
  background-color: var(--bg-card);
  overflow: hidden;

  .title-line {
    width: 6rpx;
    height: 28rpx;
    border-radius: 3rpx;
  }

  .card-content {
    width: 100%;
    font-size: 28rpx;
    color: var(--text-secondary);
  }
}
</style> 