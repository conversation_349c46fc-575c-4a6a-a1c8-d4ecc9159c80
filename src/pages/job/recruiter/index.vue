<template>
  <view class="container pb-100rpx">
    <uni-nav-bar
      fixed
      statusBar="true"
      :border="false"
      title=""
      left-icon="back"
      backgroundColor="var(--bg-page)"
      @clickLeft="goBack"
    >
      <view class="flex items-center justify-end">
        <view
          class="switch-btn flex-x items-center gap-10rpx"
          @click="openTalentFilterPopup"
        >
          <text
            class="i-solar:sort-horizontal-bold-duotone text-36rpx text-white"
          ></text>
          <text class="text-white">我要找工作</text>
        </view>
      </view>
    </uni-nav-bar>

    <view class="job-tools px-20rpx py-30rpx mb-20rpx">
      <view class="grid grid-cols-4">
        <view
          v-for="(tool, index) in recruiterTools"
          :key="index"
          class="job-tool-item flex flex-col items-center"
          @tap="handleToolClick(tool.id)"
        >
          <view
            class="tool-icon-circle mb-10rpx"
            :class="`bg-gradient-to-br ${tool.gradient}`"
          >
            <text :class="tool.icon"></text>
          </view>
          <text class="text-26rpx text-secondary">{{ tool.name }}</text>
        </view>
      </view>
    </view>

    <!-- RecruiterView 组件内容 -->
    <RecruiterView @openTalentFilterPopup="openTalentFilterPopup" />

    <!-- 人才筛选弹窗 -->
    <tui-bottom-popup
      :show="showTalentFilterDrawer"
      :height="1000"
      :isSafeArea="true"
      :zIndex="1002"
      :maskZIndex="1001"
      backgroundColor="#ffffff"
      @close="showTalentFilterDrawer = false"
    >
      <view class="talent-filter-popup">
        <view class="popup-header">
          <text class="header-title">筛选人才</text>
          <text
            class="close-btn i-carbon-close"
            @tap="showTalentFilterDrawer = false"
          ></text>
        </view>

        <scroll-view scroll-y class="filter-content">
          <filter-option
            title="经验要求"
            type="talentExperience"
            :options="talentExperienceOptions"
            @option-change="handleTalentOptionChange"
          />
          <filter-option
            title="年龄要求"
            type="talentAge"
            :options="talentAgeOptions"
            @option-change="handleTalentOptionChange"
          />
          <filter-option
            title="性别要求"
            type="talentGender"
            :options="talentGenderOptions"
            @option-change="handleTalentOptionChange"
          />
        </scroll-view>

        <view class="popup-footer">
          <view class="footer-btn reset-btn" @tap="resetTalentFilter"
            >重置</view
          >
          <view class="footer-btn confirm-btn" @tap="confirmTalentFilter"
            >确定</view
          >
        </view>
      </view>
    </tui-bottom-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useJobStore } from "@/stores/job";
import FilterOption from "@/components/home/<USER>";
import RecruiterView from "../components/RecruiterView.vue";
import tuiBottomPopup from "@/components/thorui/tui-bottom-popup/tui-bottom-popup.vue";

const jobStore = useJobStore();
const scrollTop = ref(0);
const searchKeyword = ref("");
const showTalentFilterDrawer = ref(false);

// 页面加载时恢复角色状态
onMounted(() => {
  jobStore.restoreRole();
});

// 页面滚动监听
const onPageScroll = (e: any) => {
  scrollTop.value = e.scrollTop;
};

// 招聘者工具 - 统一使用渐变色彩方案
const recruiterTools = [
  {
    id: "publish",
    name: "发布招聘",
    icon: "i-carbon-add-alt",
    gradient: "from-emerald-400 to-emerald-600",
  },
  {
    id: "manage",
    name: "职位管理",
    icon: "i-carbon-table",
    gradient: "from-blue-400 to-blue-600",
  },
  {
    id: "company-auth",
    name: "企业认证",
    icon: "i-carbon-certificate",
    gradient: "from-purple-400 to-purple-600",
  },
  {
    id: "resume-review",
    name: "简历查看",
    icon: "i-carbon-folder",
    gradient: "from-orange-400 to-orange-600",
  },
];

// 切换到求职者角色
const switchToJobseeker = () => {
  uni.showLoading({
    title: "切换中...",
  });
  setTimeout(() => {
    jobStore.switchRole("jobseeker");
    uni.redirectTo({
      url: "/pages/job/jobseeker/index",
      animationType: "zoom-out",
      animationDuration: 0,
    });
    uni.hideLoading();
  }, 300);
};

// 处理工具点击
const handleToolClick = (toolId) => {
  console.log("clicked tool:", toolId);
  if (toolId === "publish") {
    uni.navigateTo({
      url: `/pages/job/publish`,
    });
  } else if (toolId === "manage") {
    uni.navigateTo({
      url: `/pages/job/manage`,
    });
  } else if (toolId === "company-auth") {
    uni.navigateTo({
      url: `/pages/job/company-auth`,
    });
  } else if (toolId === "resume-review") {
    uni.navigateTo({
      url: `/pages/job/resume-review`,
    });
  } else {
    uni.navigateTo({
      url: `/pages/job/${toolId}`,
    });
  }
};

// 处理搜索
const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    uni.showToast({
      title: "请输入搜索关键词",
      icon: "none",
    });
    return;
  }

  console.log("搜索关键词:", searchKeyword.value);
  uni.navigateTo({
    url: `/pages/job/talent-search?keyword=${encodeURIComponent(
      searchKeyword.value
    )}`,
  });
};

// 人才筛选选项
const talentExperienceOptions = ref([
  { name: "不限", active: true },
  { name: "1年以内", active: false },
  { name: "1-3年", active: false },
  { name: "3-5年", active: false },
  { name: "5-10年", active: false },
  { name: "10年以上", active: false },
]);

const talentAgeOptions = ref([
  { name: "不限", active: true },
  { name: "25岁以下", active: false },
  { name: "25-35岁", active: false },
  { name: "35-45岁", active: false },
  { name: "45岁以上", active: false },
]);

const talentGenderOptions = ref([
  { name: "不限", active: true },
  { name: "男", active: false },
  { name: "女", active: false },
]);

// 打开人才筛选弹窗
const openTalentFilterPopup = () => {
  console.log("点击筛选按钮，准备打开筛选框");
  showTalentFilterDrawer.value = true;
  console.log("筛选框状态:", showTalentFilterDrawer.value);
};

// 处理人才筛选选项变更
const handleTalentOptionChange = ({ type, index }) => {
  let options;
  if (type === "talentExperience") {
    options = talentExperienceOptions.value;
  } else if (type === "talentAge") {
    options = talentAgeOptions.value;
  } else if (type === "talentGender") {
    options = talentGenderOptions.value;
  }

  if (options) {
    if (index === 0) {
      options.forEach((opt, idx) => (opt.active = idx === 0));
    } else {
      options[0].active = false;
      options[index].active = !options[index].active;
      if (!options.slice(1).some((opt) => opt.active)) {
        options[0].active = true;
      }
    }
  }
};

// 重置人才筛选
const resetTalentFilter = () => {
  talentExperienceOptions.value.forEach((opt, idx) => (opt.active = idx === 0));
  talentAgeOptions.value.forEach((opt, idx) => (opt.active = idx === 0));
  talentGenderOptions.value.forEach((opt, idx) => (opt.active = idx === 0));
};

// 获取RecruiterView组件引用
const recruiterViewRef = ref();

// 确认人才筛选
const confirmTalentFilter = () => {
  showTalentFilterDrawer.value = false;
  console.log("Applying talent filters...");

  // 刷新人才列表数据
  if (recruiterViewRef.value) {
    recruiterViewRef.value.refreshData();
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 页面生命周期
onPageScroll((e) => {
  scrollTop.value = e.scrollTop;
});
</script>

<style lang="scss" scoped>
.container {
  background-color: var(--bg-page);
  background-image: linear-gradient(
      to bottom,
      #e3eeff 0%,
      #eef3ff 10%,
      var(--bg-page) 20%
    ),
    var(--bg-page);

  .switch-btn {
    background: linear-gradient(90deg, #72edf2, #5151e5);
    color: #ffffff;
    border-radius: 36rpx;
    padding: 6rpx 18rpx;
    font-size: 26rpx;
    font-weight: 500;
  }

  .tool-icon-circle {
    width: 88rpx;
    height: 88rpx;
    border-radius: 24rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #ffffff;
    font-size: 36rpx;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.1);
      opacity: 0;
      transition: opacity 0.3s;
    }

    &:active {
      transform: translateY(2rpx) scale(0.95);
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);

      &::before {
        opacity: 1;
      }
    }
  }

  /* 人才筛选弹窗样式 */
  .talent-filter-popup {
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    display: flex;
    flex-direction: column;

    .popup-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20rpx;

      .header-title {
        font-size: 32rpx;
        font-weight: 500;
      }

      .close-btn {
        font-size: 32rpx;
        color: var(--text-info);
      }
    }

    .filter-content {
      flex: 1;
      height: 0;
    }

    .popup-footer {
      display: flex;
      height: 100rpx;
      box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

      .footer-btn {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 30rpx;
        margin: 20rpx;
        border-radius: 12rpx;
        transition: all 0.2s;

        &:active {
          transform: scale(0.95);
        }
      }

      .reset-btn {
        background-color: #f8f8f8;
        color: #333;
        border: 1rpx solid #ddd;
      }

      .confirm-btn {
        background-color: var(--primary);
        color: #fff;
      }
    }
  }
}
</style>
