/**
 * 房源模块通用类型定义
 */

type HouseType = {
    id: string;
    name: string;
    icon: string;
    url: string;
}

/**
 * 基础房源类型
 */
export interface BaseHouseItem {
    id: string;
    title?: string;
    name?: string;
    image: string;
    images?: string[];
    tags?: string[];
    area?: string | number;
    price?: string | number;
    unitPrice?: string;
    priceType?: string;
    extraInfo?: string;
    hasVideo?: boolean;
    hasVR?: boolean;
}

/**
 * 二手房房源类型
 */
export interface SecondHandHouseItem extends BaseHouseItem {
    layout?: string;
    rooms?: number;
    halls?: number;
    direction?: string;
    floor?: string;
    totalFloor?: number;
    decoration?: string;
    buildYear?: number;
    community?: string;
    location?: string;
    address?: string;
    unitPrice: string;
}

/**
 * 新房房源类型
 */
export interface NewHouseItem extends BaseHouseItem {
    location?: string;
    address?: string;
    priceRange?: string;
    openTime?: string;
    developer?: string;
    status?: string; // 在售、即将开盘、售罄等
    buildTypes?: string[]; // 高层、洋房、别墅等
    propertyType?: string; // 住宅、商住两用等
    propertyYears?: number; // 产权年限
    attentionCount?: number; // 关注人数
    specialOffer?: string; // 特价优惠信息
}

/**
 * 租房房源类型
 */
export interface RentHouseItem extends BaseHouseItem {
    layout?: string;
    rooms?: number;
    halls?: number;
    direction?: string;
    floor?: string;
    totalFloor?: number;
    decoration?: string;
    rentType?: string; // 整租、合租
    paymentMethod?: string; // 押一付三等
    checkInTime?: string;
    contactPerson?: string;
    contactPhone?: string;
    furniture?: string[]; // 家具列表
}

/**
 * 商业地产房源类型
 */
export interface CommercialHouseItem extends BaseHouseItem {
    type?: string; // 商铺、写字楼、厂房、仓库
    location?: string;
    priceType?: 'rent' | 'sale'; // 租赁或出售
    floor?: string;
    propertyFee?: string; // 物业费
    rentable?: boolean; // 可分割
    transferFee?: boolean; // 是否有转让费
    usePermit?: string[]; // 经营许可范围
    certificate?: boolean; // 是否有产权证
}

/**
 * 房源类型联合类型
 */
export type HouseItemType = BaseHouseItem | SecondHandHouseItem | NewHouseItem | RentHouseItem | CommercialHouseItem;

/**
 * 房产信息类型定义
 */
export interface EstateInfo {
    name: string;
    buildYear: number;
    totalBuildings: number;
    propertyFee: number;
    propertyCompany: string;
    developers: string;
}

/**
 * 周边设施类型定义
 */
export interface Surrounding {
    type: string;
    name: string;
    distance: string;
}

/**
 * 地理坐标类型定义
 */
export interface Coordinates {
    latitude: number;
    longitude: number;
}

/**
 * 筛选条件类型定义
 */
export interface HouseFilterType {
    area?: string;
    rentType?: string;
    propertyType?: string;
    priceRange?: string;
    price?: string;
    houseType?: string;
    orientation?: string;
    decoration?: string;
    facilities?: string[];
    floor?: string;
    paymentMethod?: string;
    tags?: string[];
    keyword?: string;
    sortBy?: string;
    sort?: string;
    dealType?: 'rent' | 'sale'; // 商业地产交易类型：租赁/出售
    more?: Record<string, any>; // 更多筛选条件
}

/**
 * 房源分页结果
 */
export interface HouseListResult {
    total: number;
    pageSize: number;
    pageNum: number;
    list: HouseItemType[];
}

/**
 * 筛选项类型
 */
export interface FilterOption {
    label: string;
    value: string;
    icon?: string;
    color?: string;
    checked?: boolean;
}

/**
 * 分类筛选项
 */
export interface FilterCategory {
    title: string;
    type: string;
    multiple: boolean;
    options: FilterOption[];
    hasMore?: boolean;
}

/**
 * 房产相关指南与文章
 */
export interface HouseGuide {
    id: string;
    title: string;
    image: string;
    description: string;
    author: string;
    viewCount: string;
    publishTime?: string;
    category?: string;
    content?: string;
} 