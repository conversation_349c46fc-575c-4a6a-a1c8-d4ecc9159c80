import { defineStore } from 'pinia'

export type UserRole = 'jobseeker' | 'recruiter'

export interface JobState {
    // 用户角色
    currentRole: UserRole
    // 求职者筛选条件
    jobseekerFilters: {
        experience: string[]
        education: string[]
        salary: string[]
        jobType: string[]
        welfare: string[]
    }
    // 招聘者筛选条件
    recruiterFilters: {
        status: string[]
        experience: string[]
        education: string[]
        location: string[]
    }
    // 意向职位
    intentionJobs: Array<{
        id: string
        name: string
        active: boolean
    }>
    // 招聘者统计数据
    recruiterStats: {
        todayViews: number
        todayResumes: number
        activeJobs: number
        totalViews: number
        totalResumes: number
    }
}

export const useJobStore = defineStore('job', {
    state: (): JobState => ({
        // 默认为求职者角色
        currentRole: 'jobseeker',

        // 求职者筛选条件
        jobseekerFilters: {
            experience: ['全部'],
            education: ['不限'],
            salary: ['全部'],
            jobType: ['全部'],
            welfare: []
        },

        // 招聘者筛选条件
        recruiterFilters: {
            status: ['全部'],
            experience: ['全部'],
            education: ['不限'],
            location: ['全部']
        },

        // 意向职位
        intentionJobs: [
            { id: '1', name: '前端开发', active: true },
            { id: '2', name: '产品经理', active: false },
            { id: '3', name: 'UI设计师', active: false },
            { id: '4', name: '销售专员', active: false }
        ],

        // 招聘者统计数据
        recruiterStats: {
            todayViews: 68,
            todayResumes: 12,
            activeJobs: 3,
            totalViews: 1240,
            totalResumes: 87
        }
    }),

    actions: {
        // 切换用户角色
        switchRole(role: UserRole) {
            this.currentRole = role
            // 持久化到本地存储
            uni.setStorageSync('job_user_role', role)
        },

        // 从本地存储恢复角色
        restoreRole() {
            const savedRole = uni.getStorageSync('job_user_role') as UserRole
            if (savedRole && ['jobseeker', 'recruiter'].includes(savedRole)) {
                this.currentRole = savedRole
            }
        },

        // 更新求职者筛选条件
        updateJobseekerFilters(type: keyof JobState['jobseekerFilters'], values: string[]) {
            this.jobseekerFilters[type] = values
        },

        // 更新招聘者筛选条件
        updateRecruiterFilters(type: keyof JobState['recruiterFilters'], values: string[]) {
            this.recruiterFilters[type] = values
        },

        // 重置筛选条件
        resetFilters() {
            if (this.currentRole === 'jobseeker') {
                this.jobseekerFilters = {
                    experience: ['全部'],
                    education: ['不限'],
                    salary: ['全部'],
                    jobType: ['全部'],
                    welfare: []
                }
            } else {
                this.recruiterFilters = {
                    status: ['全部'],
                    experience: ['全部'],
                    education: ['不限'],
                    location: ['全部']
                }
            }
        },

        // 选择意向职位
        selectIntentionJob(id: string) {
            this.intentionJobs.forEach(job => {
                job.active = job.id === id
            })
        },

        // 添加意向职位
        addIntentionJob(name: string) {
            const newJob = {
                id: Date.now().toString(),
                name,
                active: false
            }
            this.intentionJobs.push(newJob)
        },

        // 删除意向职位
        removeIntentionJob(id: string) {
            const index = this.intentionJobs.findIndex(job => job.id === id)
            if (index > -1) {
                this.intentionJobs.splice(index, 1)
            }
        },

        // 更新招聘者统计数据
        updateRecruiterStats(stats: Partial<JobState['recruiterStats']>) {
            this.recruiterStats = { ...this.recruiterStats, ...stats }
        }
    },

    getters: {
        // 当前是否为求职者
        isJobseeker: (state) => state.currentRole === 'jobseeker',

        // 当前是否为招聘者
        isRecruiter: (state) => state.currentRole === 'recruiter',

        // 获取当前角色的筛选条件
        currentFilters: (state) => {
            return state.currentRole === 'jobseeker'
                ? state.jobseekerFilters
                : state.recruiterFilters
        },

        // 获取激活的意向职位
        activeIntentionJob: (state) => {
            return state.intentionJobs.find(job => job.active)
        },

        // 获取筛选条件数量（用于显示筛选按钮状态）
        filterCount: (state) => {
            if (state.currentRole === 'jobseeker') {
                const filters = state.jobseekerFilters
                let count = 0
                if (!filters.experience.includes('全部')) count++
                if (!filters.education.includes('不限')) count++
                if (!filters.salary.includes('全部')) count++
                if (!filters.jobType.includes('全部')) count++
                if (filters.welfare.length > 0) count++
                return count
            } else {
                const filters = state.recruiterFilters
                let count = 0
                if (!filters.status.includes('全部')) count++
                if (!filters.experience.includes('全部')) count++
                if (!filters.education.includes('不限')) count++
                if (!filters.location.includes('全部')) count++
                return count
            }
        }
    }
}) 