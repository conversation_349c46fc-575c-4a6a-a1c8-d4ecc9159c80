<template>
  <view
    class="property-tag flex-x-center"
    :style="{ backgroundColor: computedBgColor }"
  >
    <text
      v-if="showIcon"
      :class="iconClass"
      :style="{ color: iconColor }"
    ></text>
    <text class="tag-text" :style="{ color: textColor }">{{ text }}</text>
  </view>
</template>

<script setup lang="ts">
import { computed } from "vue";

// 组件属性
interface Props {
  text: string; // 标签文本
  bgColor?: string; // 背景颜色
  textColor?: string; // 文字颜色
  iconColor?: string; // 图标颜色
  showIcon?: boolean; // 是否显示图标
  iconClass?: string; // 图标类名
  type?: "default" | "success" | "warning" | "danger" | "info"; // 预设类型
}

const props = withDefaults(defineProps<Props>(), {
  bgColor: "#333333",
  textColor: "#FFFFFF",
  iconColor: "#FFFFFF",
  showIcon: true,
  iconClass: "i-carbon-checkmark",
  type: "default",
});

// 根据type计算样式
const computedBgColor = computed(() => {
  if (props.bgColor !== "#333333") return props.bgColor;

  switch (props.type) {
    case "success":
      return "#4CAF50";
    case "warning":
      return "#FF9800";
    case "danger":
      return "#F44336";
    case "info":
      return "#3B7FFF";
    default:
      return "#333333";
  }
});
</script>

<style lang="scss" scoped>
.property-tag {
  display: inline-flex;
  align-items: center;
  padding: 8rpx 20rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);

  .tag-text {
    margin-left: 8rpx;
    font-size: 24rpx;
    line-height: 1.4;
  }
}
</style>
