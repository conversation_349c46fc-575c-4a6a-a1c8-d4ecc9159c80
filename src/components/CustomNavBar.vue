<template>
  <view class="custom-nav-bar">
    <!-- 状态栏，仅在非沉浸式时显示独立状态栏 -->
    <view 
      v-if="!isImmersive" 
      class="status-bar" 
      :style="{ height: statusBarHeight + 'px', backgroundColor: computedBackgroundColor }">
    </view>
    
    <tui-navigation-bar
      :isFixed="true"
      :isOpacity="isOpacity"
      :opacity="opacity"
      :backgroundColor="computedBackgroundColor"
      :dropDownHide="dropDownHide"
      :isImmersive="isImmersive"
      :maxOpacity="maxOpacity"
      :scrollTop="scrollTop"
      :title="title"
      :color="titleColor"
      :backIcon="showBack ? 'arrowleft' : ''"
      :backIconColor="backIconColor"
      @init="handleInit"
      @change="handleChange"
      @btnClick="handleBack"
    >
      <!-- 左侧内容 -->
      <template #left v-if="$slots.left">
        <slot name="left"></slot>
      </template>

      <!-- 中间内容 -->
      <template #center v-if="$slots.center">
        <slot name="center"></slot>
      </template>

      <!-- 自定义内容 -->
      <template #content v-if="$slots.content">
        <slot name="content"></slot>
      </template>

      <!-- 右侧内容 -->
      <template #right v-if="$slots.right">
        <slot name="right"></slot>
      </template>
    </tui-navigation-bar>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import tuiNavigationBar from "@/components/thorui/tui-navigation-bar/tui-navigation-bar.vue";

// 状态栏高度
const statusBarHeight = ref(0);

onMounted(() => {
  // 获取状态栏高度
  uni.getSystemInfo({
    success: (res) => {
      statusBarHeight.value = res.statusBarHeight || 20;
    }
  });
});

// 组件属性
const props = defineProps({
  // 是否背景透明
  isOpacity: {
    type: Boolean,
    default: false,
  },
  // 透明度值，isOpacity为true时生效
  opacity: {
    type: Number,
    default: 0.5,
  },
  // 背景颜色
  backgroundColor: {
    type: String,
    default: "#ffffff",
  },
  // 是否使用渐变背景
  isGradient: {
    type: Boolean,
    default: false,
  },
  // 渐变起始颜色
  gradientStart: {
    type: String,
    default: "#ff6d00",
  },
  // 渐变结束颜色
  gradientEnd: {
    type: String,
    default: "#ff9500",
  },
  // 渐变方向，可选：to right（从左到右）、to bottom（从上到下）
  gradientDirection: {
    type: String,
    default: "to right",
  },
  // 下拉隐藏NavigationBar
  dropDownHide: {
    type: Boolean,
    default: false,
  },
  // 是否沉浸式
  isImmersive: {
    type: Boolean,
    default: false,
  },
  // 最大透明度值，dropDownHide为true时使用
  maxOpacity: {
    type: Number,
    default: 1,
  },
  // 页面滚动距离，dropDownHide为true时使用
  scrollTop: {
    type: Number,
    default: 0,
  },
  // 标题
  title: {
    type: String,
    default: "",
  },
  // 标题文字颜色
  titleColor: {
    type: String,
    default: "#333",
  },
  // 是否显示返回按钮
  showBack: {
    type: Boolean,
    default: false,
  },
  // 返回图标颜色
  backIconColor: {
    type: String,
    default: "#333",
  }
});

// 事件触发
const emit = defineEmits([
  "init",
  "change",
  "back"
]);

// 计算背景颜色，支持渐变
const computedBackgroundColor = computed(() => {
  if (props.isGradient) {
    return `linear-gradient(${props.gradientDirection}, ${props.gradientStart}, ${props.gradientEnd})`;
  }
  return props.backgroundColor;
});

// 事件处理函数
const handleInit = (e: any) => {
  emit("init", e);
};

const handleChange = (e: any) => {
  emit("change", e);
};

const handleBack = () => {
  emit("back");
  if (props.showBack) {
    uni.navigateBack({
      fail: () => {
        uni.switchTab({
          url: '/pages/index/index'
        });
      }
    });
  }
};
</script>

<style lang="scss">
.custom-nav-bar {
  width: 100%;
  z-index: 9998;
}

.status-bar {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
}
</style>
