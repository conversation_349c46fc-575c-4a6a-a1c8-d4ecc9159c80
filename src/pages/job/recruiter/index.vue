<template>
  <view class="recruiter-container">
    <uni-nav-bar
      fixed
      statusBar="true"
      :border="false"
      title=""
      left-icon="back"
      :backgroundColor="scrollTop > 50 ? '#ffffff' : 'transparent'"
      @clickLeft="goBack"
    />

    <!-- 数据概览 -->
    <RecruiterOverview :stats="jobStore.recruiterStats" />

    <!-- 工具栏 -->
    <ToolGrid
      :tools="recruiterTools"
      role="recruiter"
      @tool-click="handleToolClick"
    />

    <!-- 人才筛选 -->
    <TalentFilter
      :filterTabs="talentFilterTabs"
      @tab-change="handleTalentFilterTabClick"
      @open-filter="openTalentFilterPopup"
    />

    <!-- 人才列表 -->
    <view class="talent-list">
      <TalentItem
        v-for="(talent, index) in filteredTalentList"
        :key="talent.id"
        :talent="talent"
        @click="goToTalentDetail(talent)"
        @contact="contactTalent(talent)"
      />
    </view>

    <!-- 人才筛选弹窗 -->
    <TalentFilterPopup
      v-model="showTalentFilterPopup"
      @confirm="handleTalentFilterConfirm"
      @reset="handleTalentFilterReset"
    />

    <!-- 浮动操作按钮 -->
    <view class="fab-container">
      <view class="fab-btn" @tap="goToPublishJob">
        <text class="i-carbon-add fab-icon"></text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useJobStore } from "@/stores/job";
import ToolGrid from "../components/ToolGrid.vue";
import RecruiterOverview from "../components/RecruiterOverview.vue";
import TalentFilter from "../components/TalentFilter.vue";
import TalentItem from "@/components/job/TalentItem.vue";
import TalentFilterPopup from "../components/TalentFilterPopup.vue";

const jobStore = useJobStore();
const scrollTop = ref(0);
const showTalentFilterPopup = ref(false);

// 页面加载时恢复角色状态
onMounted(() => {
  jobStore.restoreRole();
});

// 招聘者工具
const recruiterTools = [
  {
    id: "publish",
    name: "发布职位",
    icon: "i-carbon-add-alt",
    bgColor: "bg-primary",
  },
  {
    id: "manage",
    name: "职位管理",
    icon: "i-carbon-list-checked",
    bgColor: "bg-success",
  },
  {
    id: "analytics",
    name: "数据分析",
    icon: "i-carbon-analytics",
    bgColor: "bg-info",
  },
  {
    id: "company-auth",
    name: "企业认证",
    icon: "i-carbon-certificate-check",
    bgColor: "bg-warning",
  },
];

// 人才筛选标签
const talentFilterTabs = ref([
  { name: "全部", active: true, count: 156 },
  { name: "已投递", active: false, count: 23 },
  { name: "待面试", active: false, count: 8 },
  { name: "已拒绝", active: false, count: 12 },
]);

// 人才数据
const talentList = ref([
  {
    id: "talent1",
    name: "张三",
    avatar: "https://picsum.photos/seed/talent1/80/80",
    jobTitle: "前端开发工程师",
    experience: "3年",
    education: "本科",
    location: "北京",
    lastActivity: "昨天",
    skills: ["Vue", "React", "JavaScript", "TypeScript", "Node.js"],
    expectedSalary: "15-25K",
    status: "active",
    appliedJobs: ["前端开发工程师", "Web开发工程师"],
  },
  {
    id: "talent2",
    name: "李四",
    avatar: "https://picsum.photos/seed/talent2/80/80",
    jobTitle: "产品经理",
    experience: "5年",
    education: "硕士",
    location: "上海",
    lastActivity: "3天前",
    skills: ["产品设计", "需求分析", "项目管理", "用户研究"],
    expectedSalary: "20-30K",
    status: "interview",
    appliedJobs: ["高级产品经理"],
  },
  {
    id: "talent3",
    name: "王五",
    avatar: "https://picsum.photos/seed/talent3/80/80",
    jobTitle: "UI设计师",
    experience: "4年",
    education: "本科",
    location: "深圳",
    lastActivity: "1周前",
    skills: ["UI设计", "UX设计", "Figma", "Sketch", "原型设计"],
    expectedSalary: "12-20K",
    status: "active",
    appliedJobs: ["UI设计师", "视觉设计师"],
  },
]);

// 当前激活的筛选标签
const activeTabIndex = ref(0);

// 根据筛选条件过滤人才
const filteredTalentList = computed(() => {
  const activeTab = talentFilterTabs.value[activeTabIndex.value];

  if (activeTab.name === "全部") {
    return talentList.value;
  } else if (activeTab.name === "已投递") {
    return talentList.value.filter(
      (talent) => talent.appliedJobs && talent.appliedJobs.length > 0
    );
  } else if (activeTab.name === "待面试") {
    return talentList.value.filter((talent) => talent.status === "interview");
  } else if (activeTab.name === "已拒绝") {
    return talentList.value.filter((talent) => talent.status === "rejected");
  }

  return talentList.value;
});

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 处理工具点击
const handleToolClick = (tool: any) => {
  const routes = {
    publish: "/pages/job/publish",
    manage: "/pages/job/manage",
    analytics: "/pages/job/recruiter/analytics",
    "company-auth": "/pages/job/company-auth",
  };

  if (routes[tool.id]) {
    uni.navigateTo({ url: routes[tool.id] });
  }
};

// 处理人才筛选标签点击
const handleTalentFilterTabClick = (index: number) => {
  activeTabIndex.value = index;
  talentFilterTabs.value.forEach((tab, idx) => {
    tab.active = idx === index;
  });
};

// 打开人才筛选弹窗
const openTalentFilterPopup = () => {
  showTalentFilterPopup.value = true;
};

// 处理人才筛选确认
const handleTalentFilterConfirm = (filters: any) => {
  console.log("应用人才筛选条件:", filters);
  // 根据筛选条件重新获取数据
};

// 处理人才筛选重置
const handleTalentFilterReset = () => {
  jobStore.resetFilters();
};

// 跳转到人才详情
const goToTalentDetail = (talent: any) => {
  uni.navigateTo({
    url: `/pages/job/recruiter/talent-detail?id=${talent.id}`,
  });
};

// 联系人才
const contactTalent = (talent: any) => {
  uni.showToast({
    title: "已发送联系请求",
    icon: "success",
  });
};

// 跳转到发布职位
const goToPublishJob = () => {
  uni.navigateTo({
    url: "/pages/job/publish",
  });
};
</script>

<style lang="scss" scoped>
.recruiter-container {
  background: linear-gradient(to bottom, #f8faff 0%, #fff 20%);
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.talent-list {
  padding: 0 20rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.fab-container {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  z-index: 100;

  .fab-btn {
    width: 112rpx;
    height: 112rpx;
    border-radius: 56rpx;
    background: linear-gradient(135deg, var(--primary), var(--primary-400));
    box-shadow: 0 8rpx 24rpx rgba(var(--primary-rgb), 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;

    &:active {
      transform: scale(0.9);
    }

    .fab-icon {
      font-size: 48rpx;
      color: #fff;
    }
  }
}
</style>
