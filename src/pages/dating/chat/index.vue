<template>
  <view class="chat-page">
    <!-- 顶部导航 -->
    <view class="chat-header">
      <view class="header-content">
        <text class="header-title">消息</text>
        <view class="header-actions">
          <text class="i-carbon-search action-icon" @click="showSearch"></text>
          <text class="i-carbon-add action-icon" @click="showAddMenu"></text>
        </view>
      </view>
    </view>

    <!-- 搜索栏 -->
    <view v-if="showSearchBar" class="search-bar">
      <view class="search-input-wrapper">
        <text class="i-carbon-search search-icon"></text>
        <input
          v-model="searchKeyword"
          placeholder="搜索聊天记录"
          class="search-input"
          @input="onSearch"
        />
        <text class="cancel-btn" @click="hideSearch">取消</text>
      </view>
    </view>

    <!-- 消息类型切换 -->
    <view class="message-tabs">
      <view
        v-for="tab in messageTabs"
        :key="tab.value"
        class="tab-item"
        :class="{ active: activeTab === tab.value }"
        @click="switchTab(tab.value)"
      >
        <text class="tab-text">{{ tab.label }}</text>
        <view v-if="tab.count > 0" class="tab-badge">{{ tab.count }}</view>
      </view>
    </view>

    <!-- 聊天列表 -->
    <scroll-view
      scroll-y
      class="chat-list"
      refresher-enabled
      @refresherrefresh="onRefresh"
      :refresher-triggered="refreshing"
    >
      <view v-if="filteredChats.length === 0" class="empty-state">
        <text class="i-carbon-chat empty-icon"></text>
        <text class="empty-text">暂无消息</text>
        <text class="empty-desc">开始你的第一次聊天吧</text>
      </view>

      <view v-else class="chat-items">
        <view
          v-for="chat in filteredChats"
          :key="chat.id"
          class="chat-item"
          @click="openChat(chat)"
          @longpress="showChatMenu(chat)"
        >
          <view class="chat-avatar-wrapper">
            <image :src="chat.avatar" class="chat-avatar" mode="aspectFill" />
            <view v-if="chat.isOnline" class="online-indicator"></view>
            <view v-if="chat.unreadCount > 0" class="unread-badge">
              {{ chat.unreadCount > 99 ? "99+" : chat.unreadCount }}
            </view>
          </view>

          <view class="chat-content">
            <view class="chat-header-info">
              <text class="chat-name">{{ chat.name }}</text>
              <view class="chat-meta">
                <view v-if="chat.isVerified" class="verified-icon">
                  <text class="i-carbon-checkmark"></text>
                </view>
                <text class="chat-time">{{
                  formatTime(chat.lastMessageTime)
                }}</text>
              </view>
            </view>

            <view class="chat-preview">
              <view
                v-if="chat.lastMessageType === 'image'"
                class="message-type"
              >
                <text class="i-carbon-image"></text>
                <text>[图片]</text>
              </view>
              <view
                v-else-if="chat.lastMessageType === 'voice'"
                class="message-type"
              >
                <text class="i-carbon-microphone"></text>
                <text>[语音]</text>
              </view>
              <text v-else class="last-message">{{ chat.lastMessage }}</text>
            </view>
          </view>

          <view v-if="chat.isPinned" class="pin-indicator">
            <text class="i-carbon-pin"></text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作菜单 -->
    <view v-if="showMenu" class="action-menu" @click="hideMenu">
      <view class="menu-content" @click.stop>
        <view class="menu-item" @click="startNewChat">
          <text class="i-carbon-user-plus menu-icon"></text>
          <text>发起聊天</text>
        </view>
        <view class="menu-item" @click="viewLikedUsers">
          <text class="i-carbon-favorite menu-icon"></text>
          <text>我的喜欢</text>
        </view>
        <view class="menu-item" @click="viewVisitors">
          <text class="i-carbon-view menu-icon"></text>
          <text>访客记录</text>
        </view>
      </view>
    </view>

    <!-- 聊天操作菜单 -->
    <view v-if="selectedChat" class="chat-menu" @click="hideChatMenu">
      <view class="chat-menu-content" @click.stop>
        <view class="menu-item" @click="pinChat(selectedChat)">
          <text class="i-carbon-pin menu-icon"></text>
          <text>{{ selectedChat.isPinned ? "取消置顶" : "置顶聊天" }}</text>
        </view>
        <view class="menu-item" @click="muteChat(selectedChat)">
          <text class="i-carbon-notification-off menu-icon"></text>
          <text>{{ selectedChat.isMuted ? "取消免打扰" : "消息免打扰" }}</text>
        </view>
        <view class="menu-item danger" @click="deleteChat(selectedChat)">
          <text class="i-carbon-trash-can menu-icon"></text>
          <text>删除聊天</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";

// 响应式数据
const showSearchBar = ref(false);
const searchKeyword = ref("");
const activeTab = ref("all");
const refreshing = ref(false);
const showMenu = ref(false);
const selectedChat = ref(null);

// 消息类型标签
const messageTabs = ref([
  { label: "全部", value: "all", count: 0 },
  { label: "未读", value: "unread", count: 5 },
  { label: "喜欢", value: "liked", count: 2 },
  { label: "匹配", value: "matched", count: 3 },
]);

// 模拟聊天数据
const chatList = ref([
  {
    id: "1001",
    name: "小雅",
    avatar: "https://picsum.photos/200/200?random=1",
    lastMessage: "今天天气真不错，要不要一起出去走走？",
    lastMessageTime: new Date().getTime() - 300000, // 5分钟前
    lastMessageType: "text",
    unreadCount: 2,
    isOnline: true,
    isVerified: true,
    isPinned: true,
    isMuted: false,
    type: "matched",
  },
  {
    id: "1002",
    name: "小明",
    avatar: "https://picsum.photos/200/200?random=2",
    lastMessage: "哈哈哈，你说得对",
    lastMessageTime: new Date().getTime() - 3600000, // 1小时前
    lastMessageType: "text",
    unreadCount: 0,
    isOnline: false,
    isVerified: false,
    isPinned: false,
    isMuted: false,
    type: "liked",
  },
  {
    id: "1003",
    name: "小丽",
    avatar: "https://picsum.photos/200/200?random=3",
    lastMessage: "",
    lastMessageTime: new Date().getTime() - 7200000, // 2小时前
    lastMessageType: "image",
    unreadCount: 1,
    isOnline: true,
    isVerified: true,
    isPinned: false,
    isMuted: true,
    type: "matched",
  },
  {
    id: "1004",
    name: "小王",
    avatar: "https://picsum.photos/200/200?random=4",
    lastMessage: "",
    lastMessageTime: new Date().getTime() - 86400000, // 1天前
    lastMessageType: "voice",
    unreadCount: 3,
    isOnline: false,
    isVerified: false,
    isPinned: false,
    isMuted: false,
    type: "unread",
  },
]);

// 计算属性
const filteredChats = computed(() => {
  let filtered = chatList.value;

  // 根据标签筛选
  if (activeTab.value === "unread") {
    filtered = filtered.filter((chat) => chat.unreadCount > 0);
  } else if (activeTab.value === "liked") {
    filtered = filtered.filter((chat) => chat.type === "liked");
  } else if (activeTab.value === "matched") {
    filtered = filtered.filter((chat) => chat.type === "matched");
  }

  // 根据搜索关键词筛选
  if (searchKeyword.value) {
    filtered = filtered.filter(
      (chat) =>
        chat.name.includes(searchKeyword.value) ||
        chat.lastMessage.includes(searchKeyword.value)
    );
  }

  // 置顶聊天排在前面
  return filtered.sort((a, b) => {
    if (a.isPinned && !b.isPinned) return -1;
    if (!a.isPinned && b.isPinned) return 1;
    return b.lastMessageTime - a.lastMessageTime;
  });
});

// 方法
const showSearch = () => {
  showSearchBar.value = true;
};

const hideSearch = () => {
  showSearchBar.value = false;
  searchKeyword.value = "";
};

const onSearch = () => {
  // 搜索逻辑已在计算属性中处理
};

const switchTab = (tab: string) => {
  activeTab.value = tab;
};

const onRefresh = () => {
  refreshing.value = true;
  setTimeout(() => {
    refreshing.value = false;
  }, 1000);
};

const showAddMenu = () => {
  showMenu.value = true;
};

const hideMenu = () => {
  showMenu.value = false;
};

const openChat = (chat: any) => {
  uni.navigateTo({
    url: `/pages/dating/chat/conversation?userId=${chat.id}&name=${chat.name}`,
  });
};

const showChatMenu = (chat: any) => {
  selectedChat.value = chat;
};

const hideChatMenu = () => {
  selectedChat.value = null;
};

const pinChat = (chat: any) => {
  chat.isPinned = !chat.isPinned;
  hideChatMenu();
  uni.showToast({
    title: chat.isPinned ? "已置顶" : "已取消置顶",
    icon: "none",
  });
};

const muteChat = (chat: any) => {
  chat.isMuted = !chat.isMuted;
  hideChatMenu();
  uni.showToast({
    title: chat.isMuted ? "已开启免打扰" : "已关闭免打扰",
    icon: "none",
  });
};

const deleteChat = (chat: any) => {
  uni.showModal({
    title: "确认删除",
    content: "删除后聊天记录将无法恢复",
    success: (res) => {
      if (res.confirm) {
        const index = chatList.value.findIndex((c) => c.id === chat.id);
        if (index > -1) {
          chatList.value.splice(index, 1);
        }
        hideChatMenu();
        uni.showToast({
          title: "已删除",
          icon: "none",
        });
      }
    },
  });
};

const startNewChat = () => {
  hideMenu();
  uni.navigateTo({
    url: "/pages/dating/search/index",
  });
};

const viewLikedUsers = () => {
  hideMenu();
  uni.navigateTo({
    url: "/pages/dating/liked/index",
  });
};

const viewVisitors = () => {
  hideMenu();
  uni.navigateTo({
    url: "/pages/dating/visitors/index",
  });
};

const formatTime = (timestamp: number) => {
  const now = new Date().getTime();
  const diff = now - timestamp;

  if (diff < 60000) {
    // 1分钟内
    return "刚刚";
  } else if (diff < 3600000) {
    // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`;
  } else if (diff < 86400000) {
    // 1天内
    return `${Math.floor(diff / 3600000)}小时前`;
  } else if (diff < 604800000) {
    // 1周内
    return `${Math.floor(diff / 86400000)}天前`;
  } else {
    const date = new Date(timestamp);
    return `${date.getMonth() + 1}/${date.getDate()}`;
  }
};

onMounted(() => {
  // 更新未读消息数量
  const unreadCount = chatList.value.filter(
    (chat) => chat.unreadCount > 0
  ).length;
  messageTabs.value[1].count = unreadCount;
});
</script>

<style lang="scss" scoped>
.chat-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.chat-header {
  background: white;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 30rpx;
}

.action-icon {
  font-size: 44rpx;
  color: #666;
}

.search-bar {
  background: white;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 25rpx;
  padding: 0 20rpx;
  height: 70rpx;
}

.search-icon {
  color: #999;
  font-size: 32rpx;
  margin-right: 15rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
}

.cancel-btn {
  color: #ff5778;
  font-size: 28rpx;
  margin-left: 20rpx;
}

.message-tabs {
  background: white;
  display: flex;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 25rpx 0;
  position: relative;

  &.active {
    .tab-text {
      color: #ff5778;
      font-weight: bold;
    }

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60rpx;
      height: 4rpx;
      background: #ff5778;
      border-radius: 2rpx;
    }
  }
}

.tab-text {
  font-size: 28rpx;
  color: #666;
  margin-right: 8rpx;
}

.tab-badge {
  background: #ff5778;
  color: white;
  font-size: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
}

.chat-list {
  flex: 1;
  padding: 20rpx 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 120rpx;
  color: #ddd;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
}

.chat-items {
  padding: 0 30rpx;
}

.chat-item {
  background: white;
  border-radius: 15rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.chat-avatar-wrapper {
  position: relative;
  flex-shrink: 0;
}

.chat-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}

.online-indicator {
  position: absolute;
  bottom: 5rpx;
  right: 5rpx;
  width: 20rpx;
  height: 20rpx;
  background: #4cd964;
  border: 3rpx solid white;
  border-radius: 50%;
}

.unread-badge {
  position: absolute;
  top: -5rpx;
  right: -5rpx;
  background: #ff5778;
  color: white;
  font-size: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
}

.chat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  min-width: 0;
}

.chat-header-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-name {
  font-size: 30rpx;
  font-weight: bold;
}

.chat-meta {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.verified-icon {
  background: #ff5778;
  color: white;
  font-size: 16rpx;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chat-time {
  font-size: 24rpx;
  color: #999;
}

.chat-preview {
  display: flex;
  align-items: center;
}

.message-type {
  display: flex;
  align-items: center;
  gap: 8rpx;
  color: #999;
  font-size: 26rpx;
}

.last-message {
  font-size: 26rpx;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pin-indicator {
  position: absolute;
  top: 15rpx;
  right: 15rpx;
  color: #ff5778;
  font-size: 24rpx;
}

.action-menu,
.chat-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.menu-content,
.chat-menu-content {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  width: 100%;
  padding: 30rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 30rpx;
  &:last-child {
    border-bottom: none;
  }

  &.danger {
    color: #ff4757;
  }
}

.menu-icon {
  font-size: 40rpx;
}
</style>
