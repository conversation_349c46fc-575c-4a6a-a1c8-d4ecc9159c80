<template>
  <view class="talent-item" @tap="handleTap" v-if="talent">
    <!-- 人才主要信息 -->
    <view class="talent-header">
      <view class="talent-main-info">
        <view class="talent-name-section">
          <text class="talent-name">{{ talent.name }}</text>
          <view class="talent-tags">
            <view class="experience-tag">{{ talent.experience }}经验</view>
            <view
              v-if="talent.status === 'interview'"
              class="status-tag interview-tag"
            >
              面试中
            </view>
            <view
              v-else-if="talent.status === 'rejected'"
              class="status-tag rejected-tag"
            >
              已拒绝
            </view>
          </view>
        </view>

        <view class="talent-job-info">
          <text class="job-title">{{ talent.jobTitle }}</text>
        </view>

        <view class="talent-basic-info">
          <text class="info-item">{{ talent.education }}</text>
          <text class="divider">·</text>
          <text class="info-item">{{ talent.location }}</text>
        </view>
      </view>

      <!-- 头像区域 -->
      <view class="talent-avatar-section">
        <image
          class="talent-avatar"
          :src="talent.avatar || getDefaultAvatar(talent.gender)"
          mode="aspectFill"
        />
      </view>
    </view>

    <!-- 技能标签 -->
    <view class="talent-skills">
      <view
        v-for="(skill, index) in talent.skills.slice(0, 4)"
        :key="index"
        class="skill-tag"
      >
        {{ skill }}
      </view>
      <view v-if="talent.skills.length > 4" class="skill-more">
        +{{ talent.skills.length - 4 }}
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="talent-footer">
      <view class="last-activity">
        <text class="i-carbon-time activity-icon"></text>
        <text class="activity-text">{{ talent.lastActivity }}活跃</text>
      </view>

      <view class="talent-actions">
        <view class="action-btn secondary-btn" @tap.stop="viewResume">
          <text class="i-carbon-document"></text>
          <text>简历</text>
        </view>
        <view class="action-btn primary-btn" @tap.stop="handleContact">
          <text class="i-carbon-chat"></text>
          <text>联系</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
const props = defineProps({
  talent: {
    type: Object,
    required: true,
    default: () => ({
      name: "",
      gender: "男",
      avatar: "",
      jobTitle: "",
      experience: "",
      education: "",
      location: "",
      lastActivity: "",
      skills: [],
      status: "active",
    }),
  },
});

const emit = defineEmits(["click", "contact"]);

const handleTap = () => {
  if (!props.talent) return;
  emit("click", props.talent);
};

const handleContact = () => {
  if (!props.talent) return;
  emit("contact", props.talent);
};

const viewResume = () => {
  if (!props.talent || !props.talent.id) return;
  uni.navigateTo({
    url: `/pages/job/resume-view?talentId=${props.talent.id}`,
  });
};

// 获取默认头像
const getDefaultAvatar = (gender: string = "男") => {
  return gender === "女"
    ? "/static/images/avatar-female.png"
    : "/static/images/avatar-male.png";
};
</script>

<style lang="scss" scoped>
.talent-item {
  background-color: $bg-card;
  border-radius: $radius-lg;
  padding: $spacing-20;
  margin-bottom: $spacing-16;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.2s;

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  }

  .talent-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: $spacing-16;
    gap: $spacing-16;
  }

  .talent-main-info {
    flex: 1;
    min-width: 0;

    .talent-name-section {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: $spacing-8;

      .talent-name {
        font-size: $font-size-lg;
        font-weight: 600;
        color: $text-base;
        flex: 1;
        min-width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .talent-tags {
        display: flex;
        align-items: center;
        gap: $spacing-8;
        flex-shrink: 0;
      }
    }

    .job-title {
      font-size: $font-size-base;
      color: $text-secondary;
      margin-bottom: $spacing-8;
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .talent-basic-info {
      display: flex;
      align-items: center;
      font-size: $font-size-sm;
      color: $text-info;

      .info-item {
        flex-shrink: 0;
      }

      .divider {
        margin: 0 $spacing-8;
        flex-shrink: 0;
      }
    }
  }

  .talent-avatar-section {
    flex-shrink: 0;

    .talent-avatar {
      width: 100rpx;
      height: 100rpx;
      border-radius: 50%;
      background-color: $bg-tag;
      border: 2rpx solid $bg-card;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    }
  }

  .experience-tag {
    background-color: $primary-light;
    color: $primary;
    font-size: $font-size-xs;
    padding: 4rpx $spacing-8;
    border-radius: $radius-sm;
    font-weight: 500;
  }

  .status-tag {
    font-size: $font-size-xs;
    padding: 4rpx $spacing-8;
    border-radius: $radius-sm;
    font-weight: 500;

    &.interview-tag {
      background-color: $warning-light;
      color: $text-yellow;
    }

    &.rejected-tag {
      background-color: $danger-light;
      color: $text-red;
    }
  }

  .talent-skills {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-8;
    margin-bottom: $spacing-16;

    .skill-tag {
      background-color: $bg-tag;
      color: $text-secondary;
      font-size: $font-size-xs;
      padding: $spacing-6 $spacing-10;
      border-radius: $radius;
      border: 1rpx solid $border-color;
      transition: all 0.2s;
    }

    .skill-more {
      background-color: $primary-light;
      color: $primary;
      font-size: $font-size-xs;
      padding: $spacing-6 $spacing-10;
      border-radius: $radius;
      font-weight: 500;
    }
  }

  .talent-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .last-activity {
      display: flex;
      align-items: center;
      color: $text-grey;
      font-size: $font-size-xs;

      .activity-icon {
        font-size: $font-size-sm;
        margin-right: $spacing-4;
      }
    }

    .talent-actions {
      display: flex;
      gap: $spacing-10;

      .action-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4rpx;
        padding: $spacing-8 $spacing-12;
        border-radius: $radius;
        font-size: $font-size-xs;
        font-weight: 500;
        transition: all 0.2s;

        &:active {
          transform: scale(0.95);
        }
      }

      .secondary-btn {
        background-color: $bg-tag;
        color: $text-secondary;
        border: 1rpx solid $border-color;

        &:active {
          background-color: $bg-search;
        }
      }

      .primary-btn {
        background: linear-gradient(135deg, $primary, $primary-500);
        color: $text-inverse;
        box-shadow: 0 2rpx 8rpx rgba($primary, 0.2);

        &:active {
          box-shadow: 0 1rpx 4rpx rgba($primary, 0.15);
        }
      }
    }
  }
}
</style>
