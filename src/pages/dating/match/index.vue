<template>
  <view class="match-page">
    <!-- 顶部导航 -->
    <view class="match-header">
      <view class="header-content">
        <text class="header-title">匹配</text>
        <view class="header-actions">
          <text class="i-carbon-filter action-icon" @click="showFilter"></text>
          <text
            class="i-carbon-refresh action-icon"
            @click="refreshMatches"
          ></text>
        </view>
      </view>
    </view>

    <!-- 匹配统计 -->
    <view class="match-stats">
      <view class="stat-card">
        <text class="stat-number">{{ todayMatches }}</text>
        <text class="stat-label">今日新匹配</text>
      </view>
      <view class="stat-card">
        <text class="stat-number">{{ totalMatches }}</text>
        <text class="stat-label">总匹配数</text>
      </view>
      <view class="stat-card">
        <text class="stat-number">{{ mutualLikes }}</text>
        <text class="stat-label">互相喜欢</text>
      </view>
    </view>

    <!-- 卡片匹配区域 -->
    <view class="card-container">
      <view v-if="currentUsers.length === 0" class="empty-state">
        <text class="i-carbon-favorite empty-icon"></text>
        <text class="empty-text">暂无更多匹配</text>
        <text class="empty-desc">试试调整筛选条件</text>
        <view class="refresh-btn" @click="refreshMatches">
          <text class="i-carbon-refresh"></text>
          <text>刷新</text>
        </view>
      </view>

      <view v-else class="cards-stack">
        <view
          v-for="(user, index) in currentUsers"
          :key="user.id"
          class="user-card"
          :class="{ 'card-top': index === currentIndex }"
          :style="getCardStyle(index)"
          @touchstart="onTouchStart"
          @touchmove="onTouchMove"
          @touchend="onTouchEnd"
        >
          <!-- 用户照片 -->
          <view class="card-image-container">
            <image :src="user.avatar" class="card-image" mode="aspectFill" />

            <!-- 操作提示 -->
            <view
              v-if="swipeDirection"
              class="swipe-indicator"
              :class="swipeDirection"
            >
              <view v-if="swipeDirection === 'like'" class="like-indicator">
                <text class="i-carbon-favorite"></text>
                <text>喜欢</text>
              </view>
              <view v-else class="pass-indicator">
                <text class="i-carbon-close"></text>
                <text>跳过</text>
              </view>
            </view>

            <!-- 用户标签 -->
            <view class="card-badges">
              <view v-if="user.isVip" class="vip-badge">
                <text class="i-carbon-star-filled"></text>
                <text>VIP</text>
              </view>
              <view v-if="user.isVerified" class="verified-badge">
                <text class="i-carbon-checkmark"></text>
                <text>已认证</text>
              </view>
              <view v-if="user.isOnline" class="online-badge">
                <text class="online-dot"></text>
                <text>在线</text>
              </view>
            </view>
          </view>

          <!-- 用户信息 -->
          <view class="card-info">
            <view class="user-basic">
              <text class="user-name">{{ user.name }}</text>
              <text class="user-age">{{ user.age }}岁</text>
            </view>

            <view class="user-details">
              <view class="detail-item">
                <text class="i-carbon-location detail-icon"></text>
                <text class="detail-text">{{ user.distance }}</text>
              </view>
              <view class="detail-item">
                <text class="i-carbon-user detail-icon"></text>
                <text class="detail-text">{{ user.job }}</text>
              </view>
              <view class="detail-item">
                <text class="i-carbon-education detail-icon"></text>
                <text class="detail-text">{{ user.education }}</text>
              </view>
            </view>

            <view class="user-tags">
              <view
                v-for="(tag, tagIndex) in user.tags.slice(0, 3)"
                :key="tagIndex"
                class="user-tag"
              >
                {{ tag }}
              </view>
            </view>

            <view v-if="user.introduction" class="user-intro">
              <text class="intro-text">{{ user.introduction }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="action-buttons">
      <view class="action-btn pass-btn" @click="passUser">
        <text class="i-carbon-close"></text>
      </view>
      <view class="action-btn super-like-btn" @click="superLikeUser">
        <text class="i-carbon-star-filled"></text>
      </view>
      <view class="action-btn like-btn" @click="likeUser">
        <text class="i-carbon-favorite"></text>
      </view>
      <view class="action-btn message-btn" @click="messageUser">
        <text class="i-carbon-chat"></text>
      </view>
    </view>

    <!-- 匹配成功弹窗 -->
    <view v-if="showMatchModal" class="match-modal" @click="closeMatchModal">
      <view class="match-content" @click.stop>
        <view class="match-header-modal">
          <text class="match-title">匹配成功！</text>
          <text class="match-subtitle">你们互相喜欢</text>
        </view>

        <view class="match-avatars">
          <image
            :src="userInfo.avatar"
            class="match-avatar"
            mode="aspectFill"
          />
          <view class="heart-icon">
            <text class="i-carbon-favorite-filled"></text>
          </view>
          <image
            :src="matchedUser?.avatar"
            class="match-avatar"
            mode="aspectFill"
          />
        </view>

        <view class="match-actions">
          <view class="match-btn secondary" @click="closeMatchModal">
            继续匹配
          </view>
          <view class="match-btn primary" @click="startChat"> 开始聊天 </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";

// 响应式数据
const currentIndex = ref(0);
const swipeDirection = ref("");
const showMatchModal = ref(false);
const matchedUser = ref(null);

// 统计数据
const todayMatches = ref(3);
const totalMatches = ref(28);
const mutualLikes = ref(12);

// 用户信息
const userInfo = ref({
  avatar: "https://picsum.photos/200/200?random=0",
});

// 触摸相关
const touchStartX = ref(0);
const touchStartY = ref(0);
const cardTransform = ref("");

// 模拟用户数据
const allUsers = ref([
  {
    id: "1001",
    name: "小雅",
    age: 26,
    avatar: "https://picsum.photos/400/600?random=1",
    distance: "2.3km",
    job: "设计师",
    education: "本科",
    tags: ["设计", "旅行", "摄影"],
    introduction: "热爱设计和旅行，希望找到志同道合的人一起探索世界。",
    isVip: true,
    isVerified: true,
    isOnline: true,
  },
  {
    id: "1002",
    name: "小明",
    age: 28,
    avatar: "https://picsum.photos/400/600?random=2",
    distance: "1.8km",
    job: "程序员",
    education: "硕士",
    tags: ["编程", "游戏", "运动"],
    introduction: "喜欢技术和运动，寻找能一起成长的另一半。",
    isVip: false,
    isVerified: true,
    isOnline: false,
  },
  {
    id: "1003",
    name: "小丽",
    age: 25,
    avatar: "https://picsum.photos/400/600?random=3",
    distance: "3.5km",
    job: "教师",
    education: "本科",
    tags: ["阅读", "瑜伽", "烘焙"],
    introduction: "温柔的教师，喜欢安静的生活，希望找到懂我的人。",
    isVip: false,
    isVerified: false,
    isOnline: true,
  },
]);

const currentUsers = ref([...allUsers.value]);

// 计算属性
const getCardStyle = (index: number) => {
  if (index === currentIndex.value) {
    return {
      transform: cardTransform.value,
      zIndex: 10,
    };
  } else if (index === currentIndex.value + 1) {
    return {
      transform: "scale(0.95) translateY(20rpx)",
      zIndex: 9,
    };
  } else if (index === currentIndex.value + 2) {
    return {
      transform: "scale(0.9) translateY(40rpx)",
      zIndex: 8,
    };
  }
  return {
    transform: "scale(0.85) translateY(60rpx)",
    zIndex: 7,
  };
};

// 方法
const onTouchStart = (e: any) => {
  touchStartX.value = e.touches[0].clientX;
  touchStartY.value = e.touches[0].clientY;
};

const onTouchMove = (e: any) => {
  const deltaX = e.touches[0].clientX - touchStartX.value;
  const deltaY = e.touches[0].clientY - touchStartY.value;

  cardTransform.value = `translateX(${deltaX}px) translateY(${deltaY}px) rotate(${
    deltaX * 0.1
  }deg)`;

  if (Math.abs(deltaX) > 50) {
    swipeDirection.value = deltaX > 0 ? "like" : "pass";
  } else {
    swipeDirection.value = "";
  }
};

const onTouchEnd = (e: any) => {
  const deltaX = e.changedTouches[0].clientX - touchStartX.value;

  if (Math.abs(deltaX) > 100) {
    if (deltaX > 0) {
      likeUser();
    } else {
      passUser();
    }
  } else {
    cardTransform.value = "";
    swipeDirection.value = "";
  }
};

const likeUser = () => {
  const user = currentUsers.value[currentIndex.value];
  if (!user) return;

  // 模拟匹配成功
  if (Math.random() > 0.7) {
    matchedUser.value = user;
    showMatchModal.value = true;
  }

  nextUser();
};

const passUser = () => {
  nextUser();
};

const superLikeUser = () => {
  const user = currentUsers.value[currentIndex.value];
  if (!user) return;

  uni.showToast({
    title: "超级喜欢已发送",
    icon: "none",
  });

  // 超级喜欢有更高的匹配概率
  if (Math.random() > 0.5) {
    matchedUser.value = user;
    showMatchModal.value = true;
  }

  nextUser();
};

const messageUser = () => {
  const user = currentUsers.value[currentIndex.value];
  if (!user) return;

  uni.navigateTo({
    url: `/pages/dating/chat/conversation?userId=${user.id}&name=${user.name}`,
  });
};

const nextUser = () => {
  cardTransform.value = "";
  swipeDirection.value = "";
  currentIndex.value++;

  if (currentIndex.value >= currentUsers.value.length) {
    // 没有更多用户了
    currentUsers.value = [];
  }
};

const refreshMatches = () => {
  currentIndex.value = 0;
  currentUsers.value = [...allUsers.value];
  uni.showToast({
    title: "已刷新",
    icon: "none",
  });
};

const showFilter = () => {
  uni.navigateTo({
    url: "/pages/dating/search/index",
  });
};

const closeMatchModal = () => {
  showMatchModal.value = false;
  matchedUser.value = null;
};

const startChat = () => {
  if (matchedUser.value) {
    uni.navigateTo({
      url: `/pages/dating/chat/conversation?userId=${matchedUser.value.id}&name=${matchedUser.value.name}`,
    });
  }
  closeMatchModal();
};

onMounted(() => {
  // 初始化数据
});
</script>

<style lang="scss" scoped>
.match-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  display: flex;
  flex-direction: column;
}

.match-header {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 20rpx 30rpx;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 30rpx;
}

.action-icon {
  font-size: 44rpx;
  color: #666;
}

.match-stats {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
}

.stat-card {
  flex: 1;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15rpx;
  padding: 25rpx;
  text-align: center;
  backdrop-filter: blur(10px);
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #ff5778;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.card-container {
  flex: 1;
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.empty-icon {
  font-size: 120rpx;
  color: rgba(255, 255, 255, 0.8);
}

.empty-text {
  font-size: 32rpx;
  color: white;
  font-weight: bold;
}

.empty-desc {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

.refresh-btn {
  background: rgba(255, 255, 255, 0.9);
  color: #ff5778;
  padding: 15rpx 30rpx;
  border-radius: 25rpx;
  display: flex;
  align-items: center;
  gap: 10rpx;
  font-size: 28rpx;
  margin-top: 20rpx;
}

.cards-stack {
  position: relative;
  width: 600rpx;
  height: 800rpx;
}

.user-card {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease;
}

.card-image-container {
  position: relative;
  height: 500rpx;
}

.card-image {
  width: 100%;
  height: 100%;
}

.swipe-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 20rpx 40rpx;
  border-radius: 25rpx;
  display: flex;
  align-items: center;
  gap: 15rpx;
  font-size: 32rpx;
  font-weight: bold;

  &.like {
    background: rgba(76, 217, 100, 0.9);
    color: white;
  }

  &.pass {
    background: rgba(255, 71, 87, 0.9);
    color: white;
  }
}

.card-badges {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.vip-badge,
.verified-badge,
.online-badge {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 15rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  backdrop-filter: blur(10px);
}

.vip-badge {
  background: rgba(255, 215, 0, 0.9);
  color: white;
}

.verified-badge {
  background: rgba(255, 87, 120, 0.9);
  color: white;
}

.online-badge {
  background: rgba(76, 217, 100, 0.9);
  color: white;
}

.online-dot {
  width: 12rpx;
  height: 12rpx;
  background: white;
  border-radius: 50%;
}

.card-info {
  padding: 30rpx;
  height: 300rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.user-basic {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
}

.user-age {
  font-size: 28rpx;
  color: #666;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.detail-icon {
  font-size: 24rpx;
  color: #999;
  width: 24rpx;
}

.detail-text {
  font-size: 26rpx;
  color: #666;
}

.user-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.user-tag {
  background: #f0f0f0;
  color: #666;
  font-size: 22rpx;
  padding: 6rpx 15rpx;
  border-radius: 15rpx;
}

.user-intro {
  flex: 1;
}

.intro-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 30rpx;
  padding: 40rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

.action-btn {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 44rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.2);
  transition: transform 0.2s;

  &:active {
    transform: scale(0.95);
  }
}

.pass-btn {
  background: #ff4757;
  color: white;
}

.super-like-btn {
  background: linear-gradient(135deg, #ffd700, #ffb347);
  color: white;
}

.like-btn {
  background: #4cd964;
  color: white;
}

.message-btn {
  background: #4a90e2;
  color: white;
}

.match-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.match-content {
  background: white;
  border-radius: 20rpx;
  padding: 50rpx;
  margin: 30rpx;
  text-align: center;
  max-width: 600rpx;
}

.match-header-modal {
  margin-bottom: 40rpx;
}

.match-title {
  display: block;
  font-size: 42rpx;
  font-weight: bold;
  color: #ff5778;
  margin-bottom: 15rpx;
}

.match-subtitle {
  font-size: 28rpx;
  color: #666;
}

.match-avatars {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 30rpx;
  margin-bottom: 40rpx;
}

.match-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid #ff5778;
}

.heart-icon {
  font-size: 40rpx;
  color: #ff5778;
  animation: heartbeat 1s infinite;
}

@keyframes heartbeat {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

.match-actions {
  display: flex;
  gap: 20rpx;
}

.match-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 25rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;

  &.primary {
    background: #ff5778;
    color: white;
  }

  &.secondary {
    background: #f5f5f5;
    color: #666;
  }
}
</style>
