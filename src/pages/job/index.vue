<template>
  <view class="container pb-100rpx">
    <uni-nav-bar
      fixed
      statusBar="true"
      :border="false"
      title=""
      left-icon="back"
      :backgroundColor="scrollTop > 50 ? '#ffffff' : 'transparent'"
      @clickLeft="goBack"
    />

    <!-- 搜索框和角色切换按钮 -->
    <view class="search-header px-20rpx pt-20rpx">
      <view class="search-box">
        <view class="search-input-box">
          <text class="i-solar:minimalistic-magnifer-linear search-icon"></text>
          <input
            class="search-input"
            type="text"
            placeholder="搜索职位、公司、技能"
            v-model="searchKeyword"
            @confirm="handleSearch"
          />
        </view>
        <!-- 角色切换按钮 - 水平布局 -->
        <view class="role-switch-container">
          <view
            class="role-switch-btn"
            :class="{ active: userRole === 'recruiter' }"
            @tap="switchRole('recruiter')"
          >
            <text>{{
              userRole === "jobseeker" ? "我要招人" : "我要找工作"
            }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 重要功能入口 - 求职者 -->
    <view
      v-if="userRole === 'jobseeker'"
      class="job-tools px-20rpx py-30rpx mb-20rpx"
    >
      <view class="grid grid-cols-5">
        <view
          v-for="(tool, index) in jobSeekerTools"
          :key="index"
          class="job-tool-item flex flex-col items-center"
          @tap="handleToolClick(tool.id)"
        >
          <view class="tool-icon-circle mb-10rpx" :class="tool.bgColor">
            <text :class="tool.icon"></text>
          </view>
          <text class="text-26rpx text-secondary">{{ tool.name }}</text>
        </view>
      </view>
    </view>

    <!-- 重要功能入口 - 招聘者 -->
    <view v-else class="job-tools px-20rpx py-30rpx mb-20rpx">
      <view class="grid grid-cols-4">
        <view
          v-for="(tool, index) in recruiterTools"
          :key="index"
          class="job-tool-item flex flex-col items-center"
          @tap="handleToolClick(tool.id)"
        >
          <view class="tool-icon-circle mb-10rpx" :class="tool.bgColor">
            <text :class="tool.icon"></text>
          </view>
          <text class="text-26rpx text-secondary">{{ tool.name }}</text>
        </view>
      </view>
    </view>

    <!-- 角色内容 -->
    <JobseekerView
      v-if="userRole === 'jobseeker'"
      @openFilterPopup="openFilterPopup"
    />

    <RecruiterView v-else @openTalentFilterPopup="openTalentFilterPopup" />

    <!-- 筛选弹出层 -->
    <tui-bottom-popup
      :show="showFilterPopup"
      :height="1300"
      :isSafeArea="true"
      :zIndex="1002"
      :maskZIndex="1001"
      backgroundColor="#ffffff"
      @close="showFilterPopup = false"
    >
      <view class="filter-popup-content">
        <view
          class="filter-popup-header flex justify-between items-center px-30rpx py-20rpx"
        >
          <text class="text-32rpx font-bold">筛选</text>
          <text
            class="close-btn i-carbon-close"
            @tap="showFilterPopup = false"
          ></text>
        </view>

        <!-- 筛选选项 -->
        <scroll-view scroll-y enable-flex class="filter-options-scroll">
          <filter-option
            title="经验要求"
            type="experience"
            :options="experienceOptions"
            @option-change="handleOptionChange"
          />

          <filter-option
            title="职位类型"
            type="jobType"
            :options="jobTypeOptions"
            @option-change="handleOptionChange"
          />

          <filter-option
            title="学历要求"
            type="education"
            :options="educationOptions"
            @option-change="handleOptionChange"
          />

          <filter-option
            title="薪资待遇"
            type="salary"
            :options="salaryOptions"
            @option-change="handleOptionChange"
          />

          <filter-option
            title="公司规模"
            type="companySize"
            :options="companySizeOptions"
            @option-change="handleOptionChange"
          />

          <filter-option
            title="福利待遇"
            type="welfare"
            :options="welfareOptions"
            :isLastSection="true"
            @option-change="handleOptionChange"
          />
        </scroll-view>

        <!-- 操作按钮 -->
        <view class="filter-actions flex px-30rpx py-20rpx">
          <view
            class="reset-btn flex-1 text-center py-20rpx mr-20rpx"
            @tap="resetFilter"
            >清除</view
          >
          <view
            class="confirm-btn flex-1 text-center py-20rpx"
            @tap="confirmFilter"
            >确定</view
          >
        </view>
      </view>
    </tui-bottom-popup>
  </view>
</template>

<script setup lang="ts">
import tuiBottomPopup from "@/components/thorui/tui-bottom-popup/tui-bottom-popup.vue";
import FilterOption from "@/components/home/<USER>";
import JobseekerView from "./components/JobseekerView.vue";
import RecruiterView from "./components/RecruiterView.vue";

const scrollTop = ref(0);
const searchKeyword = ref("");
const userRole = ref("jobseeker"); // 默认为求职者角色

// 定义props接收是否显示广告banner
const props = defineProps({
  showBanner: {
    type: Boolean,
    default: true,
  },
});

const goBack = () => {
  uni.navigateBack();
};

onPageScroll((e) => {
  scrollTop.value = e.scrollTop;
});

// 广告轮播图数据
const adBannerList = [
  {
    image: "https://picsum.photos/seed/job-ad1/700/238",
    link: "https://example.com/job/ad1",
  },
  {
    image: "https://picsum.photos/seed/job-ad2/700/238",
    link: "https://example.com/job/ad2",
  },
  {
    image: "https://picsum.photos/seed/job-ad3/700/238",
    link: "https://example.com/job/ad3",
  },
];

// 求职者功能工具
const jobSeekerTools = [
  {
    id: "resume",
    name: "在线简历",
    icon: "i-carbon-document",
    bgColor: "bg-blue-500",
  },
  {
    id: "part-time",
    name: "兼职信息",
    icon: "i-carbon-time",
    bgColor: "bg-green-500",
  },
  {
    id: "factory-work",
    name: "普工招聘",
    icon: "i-carbon-tools",
    bgColor: "bg-cyan-500",
  },
  {
    id: "temp-work",
    name: "临时工",
    icon: "i-carbon-calendar",
    bgColor: "bg-purple-500",
  },
  {
    id: "job-fair",
    name: "线下招聘会",
    icon: "i-carbon-event",
    bgColor: "bg-indigo-500",
  },
];

// 招聘者功能工具
const recruiterTools = [
  {
    id: "publish",
    name: "发布招聘",
    icon: "i-carbon-add-alt",
    bgColor: "bg-orange-500",
  },
  {
    id: "manage",
    name: "职位管理",
    icon: "i-carbon-table",
    bgColor: "bg-amber-500",
  },
  {
    id: "company-auth",
    name: "企业认证",
    icon: "i-carbon-certificate",
    bgColor: "bg-red-500",
  },
  {
    id: "resume-review",
    name: "简历查看",
    icon: "i-carbon-folder",
    bgColor: "bg-rose-500",
  },
  {
    id: "talent-search",
    name: "人才搜索",
    icon: "i-carbon-search",
    bgColor: "bg-pink-500",
  },
];

// 推荐职位
const recommendJobs = [
  {
    id: "job1001",
    title: "前端开发工程师",
    isUrgent: true,
    salary: "15-25K·13薪",
    area: "海淀区",
    companyLogo: "https://picsum.photos/seed/company1/80/80",
    tags: ["3-5年", "本科", "前端开发", "Vue", "React"],
    companyName: "字节跳动",
    industry: "互联网",
    publishTime: "1小时前",
  },
  {
    id: "job1002",
    title: "销售经理",
    isUrgent: false,
    salary: "10-15K·底薪+提成",
    area: "朝阳区",
    companyLogo: "https://picsum.photos/seed/company2/80/80",
    tags: ["1-3年", "大专", "销售", "市场拓展"],
    companyName: "新东方教育",
    industry: "教育培训",
    publishTime: "3小时前",
  },
  {
    id: "job1003",
    title: "Java开发工程师",
    isUrgent: true,
    salary: "20-35K·14薪",
    area: "中关村",
    companyLogo: "https://picsum.photos/seed/company3/80/80",
    tags: ["5-10年", "本科", "Java", "微服务", "Spring"],
    companyName: "阿里巴巴",
    industry: "互联网",
    publishTime: "昨天",
  },
  {
    id: "job1004",
    title: "UI设计师",
    isUrgent: false,
    salary: "12-18K·13薪",
    area: "西城区",
    companyLogo: "https://picsum.photos/seed/company4/80/80",
    tags: ["3-5年", "本科", "UI设计", "Photoshop", "Sketch"],
    companyName: "腾讯科技",
    industry: "互联网",
    publishTime: "昨天",
  },
  {
    id: "job1005",
    title: "产品经理",
    isUrgent: false,
    salary: "15-25K·13薪",
    area: "海淀区",
    companyLogo: "https://picsum.photos/seed/company5/80/80",
    tags: ["3-5年", "本科", "产品经理", "需求分析", "项目管理"],
    companyName: "字节跳动",
    industry: "互联网",
    publishTime: "昨天",
  },
  {
    id: "job1006",
    title: "运营总监",
    isUrgent: true,
    salary: "25-35K·14薪",
    area: "朝阳区",
    companyLogo: "https://picsum.photos/seed/company6/80/80",
    tags: ["5-10年", "本科", "运营", "数据分析", "团队管理"],
    companyName: "美团点评",
    industry: "互联网",
    publishTime: "2天前",
  },
  {
    id: "job1007",
    title: "前端工程师",
    isUrgent: false,
    salary: "15-25K·13薪",
    area: "海淀区",
    companyLogo: "https://picsum.photos/seed/company7/80/80",
    tags: ["3-5年", "本科", "Vue", "React", "TypeScript"],
    companyName: "滴滴出行",
    industry: "互联网",
    publishTime: "3天前",
  },
  {
    id: "job1008",
    title: "人力资源经理",
    isUrgent: false,
    salary: "15-20K·13薪",
    area: "东城区",
    companyLogo: "https://picsum.photos/seed/company8/80/80",
    tags: ["5-10年", "本科", "招聘", "培训", "绩效"],
    companyName: "网易",
    industry: "互联网",
    publishTime: "4天前",
  },
  {
    id: "job1009",
    title: "算法工程师",
    isUrgent: true,
    salary: "30-50K·15薪",
    area: "中关村",
    companyLogo: "https://picsum.photos/seed/company9/80/80",
    tags: ["3-5年", "硕士", "机器学习", "深度学习", "Python"],
    companyName: "华为",
    industry: "通信/硬件",
    publishTime: "5天前",
  },
  {
    id: "job1010",
    title: "财务主管",
    isUrgent: false,
    salary: "12-18K·13薪",
    area: "丰台区",
    companyLogo: "https://picsum.photos/seed/company10/80/80",
    tags: ["5-10年", "本科", "会计", "财务", "审计"],
    companyName: "小米科技",
    industry: "互联网/硬件",
    publishTime: "1周前",
  },
];

// 热门企业
const hotCompanies = [
  {
    name: "腾讯科技",
    image: "https://picsum.photos/seed/tencent/350/150",
    jobsCount: "238",
    industry: "互联网",
    size: "10000人以上",
  },
  {
    name: "百度",
    image: "https://picsum.photos/seed/baidu/350/150",
    jobsCount: "156",
    industry: "互联网",
    size: "10000人以上",
  },
  {
    name: "京东",
    image: "https://picsum.photos/seed/jd/350/150",
    jobsCount: "189",
    industry: "互联网/电商",
    size: "10000人以上",
  },
];

// 弹出层控制
const showFilterPopup = ref(false);

// 筛选选项
const experienceOptions = [
  { name: "全部", active: true },
  { name: "1年以内", active: false },
  { name: "1-3年", active: false },
  { name: "3-5年", active: false },
  { name: "5-10年", active: false },
  { name: "10年以上", active: false },
  { name: "应届生", active: false },
  { name: "在校生", active: false },
];

// 职位类型选项
const jobTypeOptions = [
  { name: "全部", active: true },
  { name: "全职", active: false },
  { name: "兼职", active: false },
  { name: "实习", active: false },
  { name: "临时工", active: false },
];

// 学历要求选项
const educationOptions = [
  { name: "不限", active: true },
  { name: "高中", active: false },
  { name: "中专", active: false },
  { name: "大专", active: false },
  { name: "本科", active: false },
  { name: "硕士及以上", active: false },
];

// 福利待遇选项
const welfareOptions = [
  { name: "五险一金", active: false },
  { name: "包吃", active: false },
  { name: "包住", active: false },
  { name: "年终奖", active: false },
  { name: "加班费", active: false },
  { name: "定期体检", active: false },
  { name: "带薪年假", active: false },
  { name: "交通补贴", active: false },
  { name: "餐补", active: false },
  { name: "通讯补贴", active: false },
  { name: "医疗保险", active: false },
  { name: "社保", active: false },
];

const salaryOptions = [
  { name: "全部", active: true },
  { name: "15K以下", active: false },
  { name: "15-25K", active: false },
  { name: "25-35K", active: false },
  { name: "35-45K", active: false },
  { name: "45K以上", active: false },
];

const companySizeOptions = [
  { name: "全部", active: true },
  { name: "0-20人", active: false },
  { name: "20-99人", active: false },
  { name: "100-499人", active: false },
  { name: "500-999人", active: false },
  { name: "1000-9999人", active: false },
  { name: "10000人以上", active: false },
];

// 切换角色
const switchRole = (role) => {
  userRole.value = role;
};

// 处理工具点击
const handleToolClick = (toolId) => {
  console.log("clicked tool:", toolId);
  // 不同工具的跳转处理
  if (toolId === "nearby") {
    // 附近专区处理
    uni.navigateTo({
      url: `/pages/job/nearby`,
    });
  } else if (toolId === "part-time") {
    // 兼职处理
    uni.navigateTo({
      url: `/pages/job/parttime`,
    });
  } else if (toolId === "all-jobs") {
    // 全部职位处理
    uni.navigateTo({
      url: `/pages/job/all`,
    });
  } else {
    uni.navigateTo({
      url: `/pages/job/${toolId}`,
    });
  }
};

// 处理搜索
const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    uni.showToast({
      title: "请输入搜索关键词",
      icon: "none",
    });
    return;
  }

  console.log("搜索关键词:", searchKeyword.value);
  // 执行搜索操作
  uni.navigateTo({
    url: `/pages/job/search?keyword=${encodeURIComponent(searchKeyword.value)}`,
  });
};

// 切换筛选选项
const toggleFilterOption = (type, index) => {
  let options;

  if (type === "experience") {
    options = experienceOptions;
  } else if (type === "jobType") {
    options = jobTypeOptions;
  } else if (type === "education") {
    options = educationOptions;
  } else if (type === "salary") {
    options = salaryOptions;
  } else if (type === "companySize") {
    options = companySizeOptions;
  } else if (type === "welfare") {
    options = welfareOptions;
    // 福利待遇是多选，直接切换状态并返回
    options[index].active = !options[index].active;
    return;
  }

  if (index === 0) {
    // 如果是"全部"选项
    options.forEach((opt, idx) => {
      opt.active = idx === 0;
    });
  } else {
    options[0].active = false; // 取消"全部"的选中状态
    options[index].active = !options[index].active;

    // 如果没有选中任何选项，则自动选中"全部"
    if (!options.some((opt) => opt.active)) {
      options[0].active = true;
    }
  }
};

// 重置筛选
const resetFilter = () => {
  experienceOptions.forEach((opt, idx) => {
    opt.active = idx === 0;
  });

  jobTypeOptions.forEach((opt, idx) => {
    opt.active = idx === 0;
  });

  educationOptions.forEach((opt, idx) => {
    opt.active = idx === 0;
  });

  salaryOptions.forEach((opt, idx) => {
    opt.active = idx === 0;
  });

  companySizeOptions.forEach((opt, idx) => {
    opt.active = idx === 0;
  });

  // 福利待遇全部重置为未选中
  welfareOptions.forEach((opt) => {
    opt.active = false;
  });
};

// 打开筛选弹出层
const openFilterPopup = () => {
  showFilterPopup.value = true;
};

// 处理筛选选项变更
const handleOptionChange = ({ type, index }) => {
  toggleFilterOption(type, index);
};

// 确认筛选
const confirmFilter = () => {
  showFilterPopup.value = false;
  // 根据筛选条件获取数据
};

// 打开人才筛选弹窗
const openTalentFilterPopup = () => {
  // 打开人才筛选弹窗
  console.log("打开人才筛选弹窗");
};
</script>

<style lang="scss" scoped>
.container {
  background-color: var(--bg-page);
  background-image: linear-gradient(
      to bottom,
      #e3eeff 0%,
      #eef3ff 10%,
      var(--bg-page) 20%
    ),
    var(--bg-page);

  // 角色切换
  .role-switch-header {
    .role-switch-container {
      display: flex;
      background-color: #fff;
      border-radius: 32rpx;
      padding: 8rpx;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);

      .role-switch-btn {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20rpx 24rpx;
        border-radius: 24rpx;
        font-size: 28rpx;
        font-weight: 500;
        color: var(--text-secondary);
        transition: all 0.3s;

        &.active {
          background: linear-gradient(
            135deg,
            var(--primary),
            var(--primary-400)
          );
          color: #fff;
          box-shadow: 0 4rpx 12rpx rgba(var(--primary-rgb), 0.3);
        }

        &:active {
          transform: scale(0.98);
        }
      }
    }
  }

  // 搜索框样式
  .search-header {
    .search-box {
      display: flex;
      align-items: center;
      gap: 20rpx;
      width: 100%;

      .search-input-box {
        flex: 1;
        display: flex;
        align-items: center;
        background-color: #fff;
        border-radius: 36rpx;
        padding: 0 24rpx;
        height: 80rpx;
        border: 2rpx solid var(--primary-400);

        .search-icon {
          font-size: 32rpx;
          color: var(--text-info);
          margin-right: 10rpx;
        }

        .search-input {
          flex: 1;
          height: 80rpx;
          font-size: 28rpx;
          color: #333;
        }
      }

      .role-switch-container {
        .role-switch-btn {
          padding: 16rpx 24rpx;
          background: linear-gradient(
            90deg,
            var(--primary-400),
            var(--primary)
          );
          color: #fff;
          border-radius: 36rpx;
          font-size: 26rpx;
          font-weight: 500;
          box-shadow: 0 4rpx 8rpx rgba(var(--primary), 0.3);
          transition: all 0.2s;
          white-space: nowrap;

          &:active {
            transform: scale(0.95);
            opacity: 0.9;
          }

          &.active {
            background: linear-gradient(90deg, #ff6b35, #ff8c5a);
          }
        }
      }
    }
  }

  .tool-icon-circle {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: $text-inverse;
    font-size: 40rpx;
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;

    &:active {
      transform: scale(0.95);
    }
  }

  /* 筛选弹出层样式 */
  .filter-popup-content {
    background-color: $bg-card;
    height: 100%;
    display: flex;
    flex-direction: column;
    max-height: 85vh;
    height: 100%;
    padding-bottom: 100rpx;
  }

  .filter-popup-header {
    border-bottom: 1rpx solid $border-color;
  }

  .close-btn {
    font-size: 40rpx;
    color: $text-grey;
  }

  .filter-options-scroll {
    flex: 1;
    height: calc(100% - 160rpx);
  }

  .filter-actions {
    padding: 30rpx;
    border-top: 1rpx solid $border-color;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: $bg-card;
    z-index: 9;
  }

  .reset-btn {
    border: 1rpx solid $border-color;
    border-radius: 8rpx;
    font-size: 30rpx;
    color: $text-secondary;
    transition: all 0.2s;

    &:active {
      background-color: #f5f5f5;
    }
  }

  .confirm-btn {
    background-color: $primary;
    color: $text-inverse;
    border-radius: 8rpx;
    font-size: 30rpx;
    font-weight: 500;
    transition: all 0.2s;

    &:active {
      opacity: 0.8;
    }
  }
}
</style>
