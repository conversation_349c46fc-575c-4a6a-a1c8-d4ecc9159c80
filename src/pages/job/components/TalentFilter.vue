<template>
  <view class="talent-filter">
    <view class="filter-header">
      <text class="filter-title">人才筛选</text>
      <view class="filter-more" @click="openFilterPopup">
        <text class="text-secondary">筛选</text>
        <text class="i-carbon-filter ml-4rpx text-secondary"></text>
      </view>
    </view>

    <view class="talent-filter-tabs">
      <view
        v-for="(tab, index) in filterTabs"
        :key="index"
        class="talent-filter-tab"
        :class="{ 'active-tab': tab.active }"
        @click="handleTabClick(index)"
      >
        <text>{{ tab.name }}</text>
        <view v-if="tab.count" class="tab-count">{{ tab.count }}</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
interface FilterTab {
  name: string;
  active: boolean;
  count?: number;
}

interface Props {
  filterTabs: FilterTab[];
}

interface Emits {
  (e: "tab-change", index: number): void;
  (e: "open-filter"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const handleTabClick = (index: number) => {
  emit("tab-change", index);
};

const openFilterPopup = () => {
  emit("open-filter");
};
</script>

<style lang="scss" scoped>
.talent-filter {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 24rpx;
  margin: 20rpx;

  .filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    .filter-title {
      font-size: 30rpx;
      font-weight: 600;
      color: var(--text-base);
    }

    .filter-more {
      display: flex;
      align-items: center;
      padding: 12rpx 16rpx;
      border-radius: 20rpx;
      background-color: var(--bg-secondary);
      transition: all 0.2s;

      &:active {
        transform: scale(0.95);
        background-color: var(--bg-info);
      }
    }
  }

  .talent-filter-tabs {
    display: flex;
    gap: 16rpx;
    flex-wrap: wrap;

    .talent-filter-tab {
      display: flex;
      align-items: center;
      gap: 8rpx;
      padding: 16rpx 24rpx;
      background-color: var(--bg-secondary);
      border-radius: 24rpx;
      color: var(--text-secondary);
      font-size: 28rpx;
      transition: all 0.2s;
      position: relative;

      &.active-tab {
        background-color: var(--primary);
        color: #fff;

        .tab-count {
          background-color: rgba(255, 255, 255, 0.2);
          color: #fff;
        }
      }

      &:active {
        transform: scale(0.95);
      }

      .tab-count {
        background-color: var(--error);
        color: #fff;
        font-size: 20rpx;
        padding: 4rpx 8rpx;
        border-radius: 10rpx;
        min-width: 32rpx;
        text-align: center;
        line-height: 1;
      }
    }
  }
}
</style>
