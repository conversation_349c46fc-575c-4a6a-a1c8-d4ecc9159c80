<template>
  <view class="license-info-panel">
    <view v-if="licenses && licenses.length > 0">
      <view
        v-for="(license, index) in licenses"
        :key="index"
        class="license-item"
      >
        <view class="license-header">
          <text class="license-number">{{ license.number }}</text>
          <text class="license-date">发证日期：{{ license.issueDate }}</text>
        </view>
        <view class="license-content">
          <view class="license-row">
            <text class="row-label">发证机关</text>
            <text class="row-value">{{ license.authority }}</text>
          </view>
          <view class="license-row">
            <text class="row-label">预售范围</text>
            <text class="row-value">{{ license.scope }}</text>
          </view>
          <view class="license-row">
            <text class="row-label">预售户数</text>
            <text class="row-value">{{ license.unitCount }}</text>
          </view>
        </view>
      </view>
    </view>
    <view v-else class="no-license">
      <text class="no-license-text">暂无预售许可证信息</text>
    </view>
  </view>
</template>

<script setup lang="ts">
// 定义组件属性
interface License {
  number: string; // 许可证号
  issueDate: string; // 发证日期
  authority: string; // 发证机关
  scope: string; // 预售范围
  unitCount: string; // 预售户数
}

interface Props {
  licenses: License[]; // 预售许可证列表
}

const props = withDefaults(defineProps<Props>(), {
  licenses: () => [],
});
</script>

<style lang="scss" scoped>
.license-info-panel {
  .license-item {
    margin-bottom: 24rpx;
    background-color: #f9f9f9;
    border-radius: 16rpx;
    overflow: hidden;

    &:last-child {
      margin-bottom: 0;
    }

    .license-header {
      padding: 24rpx;
      background-color: #f0f0f0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .license-number {
        font-size: 30rpx;
        font-weight: 600;
      }

      .license-date {
        font-size: 26rpx;
        color: #666;
      }
    }

    .license-content {
      padding: 24rpx;

      .license-row {
        display: flex;
        align-items: flex-start;
        margin-bottom: 20rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .row-label {
          width: 180rpx;
          font-size: 28rpx;
          color: #888;
          flex-shrink: 0;
          padding-right: 24rpx;
          line-height: 1.5;
        }

        .row-value {
          flex: 1;
          font-size: 28rpx;
          text-align: left;
          line-height: 1.5;
        }
      }
    }
  }

  .no-license {
    padding: 80rpx 0;
    display: flex;
    justify-content: center;
    align-items: center;

    .no-license-text {
      font-size: 30rpx;
      color: #999;
    }
  }
}
</style>
