<template>
  <view class="profile-page">
    <!-- 顶部导航 -->
    <view class="profile-header">
      <view class="header-content">
        <text class="header-title">我的</text>
        <view class="header-actions">
          <text
            class="i-carbon-settings action-icon"
            @click="goToSettings"
          ></text>
        </view>
      </view>
    </view>

    <scroll-view scroll-y class="profile-content">
      <!-- 用户信息卡片 -->
      <view class="user-card">
        <view class="user-info">
          <view class="avatar-section" @click="changeAvatar">
            <image
              :src="userInfo.avatar"
              class="user-avatar"
              mode="aspectFill"
            />
            <view class="avatar-edit">
              <text class="i-carbon-edit"></text>
            </view>
            <view v-if="userInfo.isVip" class="vip-badge">
              <text class="i-carbon-star-filled"></text>
            </view>
          </view>

          <view class="user-details">
            <view class="user-name-section">
              <text class="user-name">{{ userInfo.name }}</text>
              <view v-if="userInfo.isVerified" class="verified-badge">
                <text class="i-carbon-checkmark"></text>
              </view>
            </view>

            <view class="user-meta">
              <text class="user-age">{{ userInfo.age }}岁</text>
              <text class="separator">·</text>
              <text class="user-location">{{ userInfo.location }}</text>
            </view>

            <view class="user-stats">
              <view class="stat-item" @click="viewVisitors">
                <text class="stat-number">{{ userInfo.visitors }}</text>
                <text class="stat-label">访客</text>
              </view>
              <view class="stat-item" @click="viewLikes">
                <text class="stat-number">{{ userInfo.likes }}</text>
                <text class="stat-label">喜欢</text>
              </view>
              <view class="stat-item" @click="viewMatches">
                <text class="stat-number">{{ userInfo.matches }}</text>
                <text class="stat-label">匹配</text>
              </view>
            </view>
          </view>
        </view>

        <view class="profile-actions">
          <view class="action-btn primary" @click="editProfile">
            <text class="i-carbon-edit"></text>
            <text>编辑资料</text>
          </view>
          <view class="action-btn secondary" @click="previewProfile">
            <text class="i-carbon-view"></text>
            <text>预览</text>
          </view>
        </view>
      </view>

      <!-- 功能菜单 -->
      <view class="menu-section">
        <view class="menu-group">
          <view class="menu-item" @click="goToVip">
            <view class="menu-icon vip-icon">
              <text class="i-carbon-star-filled"></text>
            </view>
            <text class="menu-text">VIP会员</text>
            <view class="menu-badge">升级</view>
            <text class="i-carbon-chevron-right menu-arrow"></text>
          </view>

          <view class="menu-item" @click="goToWallet">
            <view class="menu-icon wallet-icon">
              <text class="i-carbon-wallet"></text>
            </view>
            <text class="menu-text">我的钱包</text>
            <text class="menu-value">¥{{ userInfo.balance }}</text>
            <text class="i-carbon-chevron-right menu-arrow"></text>
          </view>
        </view>

        <view class="menu-group">
          <view class="menu-item" @click="goToMyEvents">
            <view class="menu-icon event-icon">
              <text class="i-carbon-calendar"></text>
            </view>
            <text class="menu-text">我的活动</text>
            <text class="i-carbon-chevron-right menu-arrow"></text>
          </view>

          <view class="menu-item" @click="goToMyMatchmaker">
            <view class="menu-icon matchmaker-icon">
              <text class="i-carbon-user-multiple"></text>
            </view>
            <text class="menu-text">我的红娘</text>
            <text class="i-carbon-chevron-right menu-arrow"></text>
          </view>

          <view class="menu-item" @click="goToBlacklist">
            <view class="menu-icon blacklist-icon">
              <text class="i-carbon-user-x"></text>
            </view>
            <text class="menu-text">黑名单</text>
            <text class="i-carbon-chevron-right menu-arrow"></text>
          </view>
        </view>

        <view class="menu-group">
          <view class="menu-item" @click="goToPrivacy">
            <view class="menu-icon privacy-icon">
              <text class="i-carbon-security"></text>
            </view>
            <text class="menu-text">隐私设置</text>
            <text class="i-carbon-chevron-right menu-arrow"></text>
          </view>

          <view class="menu-item" @click="goToHelp">
            <view class="menu-icon help-icon">
              <text class="i-carbon-help"></text>
            </view>
            <text class="menu-text">帮助中心</text>
            <text class="i-carbon-chevron-right menu-arrow"></text>
          </view>

          <view class="menu-item" @click="goToAbout">
            <view class="menu-icon about-icon">
              <text class="i-carbon-information"></text>
            </view>
            <text class="menu-text">关于我们</text>
            <text class="i-carbon-chevron-right menu-arrow"></text>
          </view>
        </view>
      </view>

      <!-- 退出登录 -->
      <view class="logout-section">
        <view class="logout-btn" @click="logout"> 退出登录 </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";

// 用户信息
const userInfo = ref({
  name: "小雅",
  age: 26,
  location: "北京·朝阳区",
  avatar: "https://picsum.photos/200/200?random=1",
  isVip: false,
  isVerified: true,
  visitors: 128,
  likes: 45,
  matches: 12,
  balance: 168.5,
});

// 方法
const goToSettings = () => {
  uni.navigateTo({
    url: "/pages/dating/settings/index",
  });
};

const changeAvatar = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ["compressed"],
    sourceType: ["album", "camera"],
    success: (res) => {
      userInfo.value.avatar = res.tempFilePaths[0];
      uni.showToast({
        title: "头像已更新",
        icon: "none",
      });
    },
  });
};

const editProfile = () => {
  uni.navigateTo({
    url: "/pages/dating/profile/edit",
  });
};

const previewProfile = () => {
  uni.navigateTo({
    url: "/pages/dating/profile/preview",
  });
};

const viewVisitors = () => {
  uni.navigateTo({
    url: "/pages/dating/visitors/index",
  });
};

const viewLikes = () => {
  uni.navigateTo({
    url: "/pages/dating/likes/index",
  });
};

const viewMatches = () => {
  uni.navigateTo({
    url: "/pages/dating/matches/index",
  });
};

const goToVip = () => {
  uni.navigateTo({
    url: "/pages/dating/vip/index",
  });
};

const goToWallet = () => {
  uni.navigateTo({
    url: "/pages/dating/wallet/index",
  });
};

const goToMyEvents = () => {
  uni.navigateTo({
    url: "/pages/dating/my-events/index",
  });
};

const goToMyMatchmaker = () => {
  uni.navigateTo({
    url: "/pages/dating/my-matchmaker/index",
  });
};

const goToBlacklist = () => {
  uni.navigateTo({
    url: "/pages/dating/blacklist/index",
  });
};

const goToPrivacy = () => {
  uni.navigateTo({
    url: "/pages/dating/privacy/index",
  });
};

const goToHelp = () => {
  uni.navigateTo({
    url: "/pages/dating/help/index",
  });
};

const goToAbout = () => {
  uni.navigateTo({
    url: "/pages/dating/about/index",
  });
};

const logout = () => {
  uni.showModal({
    title: "确认退出",
    content: "确定要退出登录吗？",
    success: (res) => {
      if (res.confirm) {
        // 清除用户数据
        uni.removeStorageSync("userToken");
        uni.showToast({
          title: "已退出登录",
          icon: "none",
        });

        // 跳转到登录页
        setTimeout(() => {
          uni.reLaunch({
            url: "/pages/login/index",
          });
        }, 1000);
      }
    },
  });
};

onMounted(() => {
  // 获取用户信息
});
</script>

<style lang="scss" scoped>
.profile-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.profile-header {
  background: white;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
}

.action-icon {
  font-size: 44rpx;
  color: #666;
}

.profile-content {
  flex: 1;
  padding: 30rpx;
}

.user-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.user-info {
  display: flex;
  gap: 30rpx;
  margin-bottom: 30rpx;
}

.avatar-section {
  position: relative;
  flex-shrink: 0;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
}

.avatar-edit {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 36rpx;
  height: 36rpx;
  background: #ff5778;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  border: 3rpx solid white;
}

.vip-badge {
  position: absolute;
  top: -5rpx;
  left: -5rpx;
  width: 32rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #ffd700, #ffb347);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18rpx;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.user-name-section {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
}

.verified-badge {
  background: #ff5778;
  color: white;
  font-size: 16rpx;
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-meta {
  display: flex;
  align-items: center;
  gap: 10rpx;
  font-size: 26rpx;
  color: #666;
}

.separator {
  color: #ddd;
}

.user-stats {
  display: flex;
  gap: 40rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.stat-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff5778;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

.profile-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 70rpx;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  font-size: 28rpx;

  &.primary {
    background: #ff5778;
    color: white;
  }

  &.secondary {
    background: #f5f5f5;
    color: #666;
  }
}

.menu-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.menu-group {
  background: white;
  border-radius: 15rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 20rpx;

  &.vip-icon {
    background: linear-gradient(135deg, #ffd700, #ffb347);
    color: white;
  }

  &.wallet-icon {
    background: #e8f5e8;
    color: #4cd964;
  }

  &.event-icon {
    background: #e6f3ff;
    color: #4a90e2;
  }

  &.matchmaker-icon {
    background: #ffe6ea;
    color: #ff5778;
  }

  &.blacklist-icon {
    background: #f0f0f0;
    color: #666;
  }

  &.privacy-icon {
    background: #f3e8ff;
    color: #8b5cf6;
  }

  &.help-icon {
    background: #fff3e0;
    color: #ff9800;
  }

  &.about-icon {
    background: #e3f2fd;
    color: #2196f3;
  }
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
}

.menu-badge {
  background: #ff5778;
  color: white;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 15rpx;
  margin-right: 15rpx;
}

.menu-value {
  font-size: 26rpx;
  color: #666;
  margin-right: 15rpx;
}

.menu-arrow {
  font-size: 24rpx;
  color: #ccc;
}

.logout-section {
  margin-top: 40rpx;
}

.logout-btn {
  background: white;
  color: #ff4757;
  font-size: 30rpx;
  text-align: center;
  padding: 25rpx;
  border-radius: 15rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
</style>
