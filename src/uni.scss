/* 颜色变量 */

/* 行为相关颜色 */
$uni-color-primary: #007aff;
$uni-color-success: #4cd964;
$uni-color-warning: #f0ad4e;
$uni-color-error: #dd524d;

// /* 文字基本颜色 */
$uni-text-color: #333; // 基本色
$uni-text-color-inverse: #fff; // 反色
$uni-text-color-grey: #999; // 辅助灰色，如加载更多的提示信息
$uni-text-color-placeholder: #808080;
$uni-text-color-disable: #c0c0c0;

// /* 背景颜色 */
// $uni-bg-color: #fff;
// $uni-bg-color-grey: #f8f8f8;
// $uni-bg-color-hover: #f1f1f1; // 点击状态颜色
// $uni-bg-color-mask: rgba(0, 0, 0, 0.4); // 遮罩颜色

// /* 边框颜色 */
// $uni-border-color: #c8c7cc;

// /* 尺寸变量 */

// /* 文字尺寸 */
// $uni-font-size-sm: 12px;
// $uni-font-size-base: 14px;
// $uni-font-size-lg: 16;

// /* 图片尺寸 */
// $uni-img-size-sm: 20px;
// $uni-img-size-base: 26px;
// $uni-img-size-lg: 40px;

// /* Border Radius */
// $uni-border-radius-sm: 2px;
// $uni-border-radius-base: 3px;
// $uni-border-radius-lg: 6px;
// $uni-border-radius-circle: 50%;

// /* 水平间距 */
// $uni-spacing-row-sm: 5px;
// $uni-spacing-row-base: 10px;
// $uni-spacing-row-lg: 15px;

// /* 垂直间距 */
// $uni-spacing-col-sm: 4px;
// $uni-spacing-col-base: 8px;
// $uni-spacing-col-lg: 12px;

// /* 透明度 */
// $uni-opacity-disabled: 0.3; // 组件禁用态的透明度

// /* 文章场景相关 */
// $uni-color-title: #2c405a; // 文章标题颜色
// $uni-font-size-title: 20px;
// $uni-color-subtitle: #555; // 二级标题颜色
// $uni-font-size-subtitle: 18px;
// $uni-color-paragraph: #3f536e; // 文章段落颜色
// $uni-font-size-paragraph: 15px;

/* 颜色变量 */

// $primary: #007aff;
// $success: #4cd964;
// $warning: #f0ad4e;
// $error: #dc2626;

/* ==================== 颜色系统 ==================== */
/* 主题色 */
$primary-50: #fff9ec;
$primary-100: #fff1d3;
$primary-200: #ffdfa5;
$primary-300: #ffc66d;
$primary-400: #ffa232;
$primary-500: #ff860a;
$primary-600: #ff6d00;
$primary-700: #cc4e02;
$primary-800: #a13d0b;
$primary-900: #82340c;
$primary-950: #461804;

$primary: $primary-600;
$vitality-orange: #f29421; // 活力橙色

/* 文本颜色 */
$text-red: #f52c37; // 红色文本，用于警告和错误
$text-green: #22c55e; // 绿色文本，用于成功和积极状态
$text-blue: #3b82f6; // 蓝色文本，用于链接和信息
$text-yellow: #eab308; // 黄色文本，用于警告
$text-purple: #8b5cf6; // 紫色文本

$text-inverse: #feffff; // 反色文本，通常用于深色背景
$text-base: #212529; // 主要文本颜色，替代#333
$text-secondary: #515359; // 次要文本颜色，替代#666
$text-info: #8e8e93; // 描述文本颜色，用于标签
$text-grey: #a1a1aa; // 占位文本颜色，替代#999
$text-disable: #d4d4d8; // 禁用状态文本颜色
$border-color: #f0f0f1; // 边框颜色

/* 背景颜色 */
$bg-page: #f4f5f9; // 页面背景色
$bg-tag: #f3f5f7; // 标签背景色
$bg-card: #feffff; // 卡片背景色
$bg-search: #f1f2f7; // 搜索框背景色
$bg-input: #f2f2f7; // 输入框背景色
$bg-mask: rgba(0, 0, 0, 0.5); // 遮罩背景色

/* 功能色背景 */
$primary-light: rgba(255, 109, 0, 0.15); // 主题色淡背景
$success-light: rgba(34, 197, 94, 0.1); // 成功色淡背景
$warning-light: rgba(234, 179, 8, 0.1); // 警告色淡背景
$danger-light: rgba(220, 38, 38, 0.1); // 危险色淡背景
$info-light: rgba(59, 130, 246, 0.1); // 信息色淡背景

/* ==================== 尺寸和间距 ==================== */
/* 圆角尺寸 */
$radius-sm: 8rpx;
$radius: 16rpx;
$radius-lg: 24rpx;
$radius-xl: 32rpx;
$radius-xxl: 40rpx;

/* 间距尺寸 */
$spacing-4: 8rpx;
$spacing-6: 12rpx;
$spacing-8: 16rpx;
$spacing-10: 20rpx;
$spacing-12: 24rpx;
$spacing-14: 28rpx;
$spacing-16: 32rpx;
$spacing-18: 36rpx;
$spacing-20: 40rpx;
$spacing-24: 48rpx;
$spacing-28: 56rpx;
$spacing-32: 64rpx;
$spacing-36: 72rpx;
$spacing-40: 80rpx;

/* 字体大小 */
$font-size-xs: 24rpx; // 12px
$font-size-sm: 26rpx; // 13px
$font-size-base: 28rpx; // 14px
$font-size-md: 30rpx; // 15px
$font-size-lg: 32rpx; // 16px
$font-size-xl: 36rpx; // 18px
$font-size-xxl: 40rpx; // 20px
