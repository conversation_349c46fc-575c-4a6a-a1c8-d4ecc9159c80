<template>
  <view class="matchmaker-page">
    <!-- 顶部导航 -->
    <view class="matchmaker-header">
      <view class="header-content">
        <text class="header-title">红娘</text>
        <view class="header-actions">
          <text class="i-carbon-search action-icon" @click="showSearch"></text>
        </view>
      </view>
    </view>

    <!-- 筛选标签 -->
    <view class="filter-tabs">
      <view
        v-for="tab in filterTabs"
        :key="tab.value"
        class="filter-tab"
        :class="{ active: activeFilter === tab.value }"
        @click="setFilter(tab.value)"
      >
        {{ tab.label }}
      </view>
    </view>

    <!-- 红娘列表 -->
    <scroll-view
      scroll-y
      class="matchmaker-list"
      refresher-enabled
      @refresherrefresh="onRefresh"
      :refresher-triggered="refreshing"
    >
      <view v-if="filteredMatchmakers.length === 0" class="empty-state">
        <text class="i-carbon-user-multiple empty-icon"></text>
        <text class="empty-text">暂无红娘</text>
        <text class="empty-desc">敬请期待更多专业红娘</text>
      </view>

      <view v-else class="matchmakers-container">
        <view
          v-for="matchmaker in filteredMatchmakers"
          :key="matchmaker.id"
          class="matchmaker-card"
          @click="viewMatchmakerDetail(matchmaker.id)"
        >
          <!-- 红娘头像和基本信息 -->
          <view class="matchmaker-header">
            <view class="avatar-section">
              <image
                :src="matchmaker.avatar"
                class="matchmaker-avatar"
                mode="aspectFill"
              />
              <view v-if="matchmaker.isOnline" class="online-indicator"></view>
              <view v-if="matchmaker.isOfficial" class="official-badge">
                <text class="i-carbon-checkmark"></text>
              </view>
            </view>

            <view class="matchmaker-info">
              <view class="name-section">
                <text class="matchmaker-name">{{ matchmaker.name }}</text>
                <view class="rating">
                  <text class="i-carbon-star-filled star-icon"></text>
                  <text class="rating-text">{{ matchmaker.rating }}</text>
                </view>
              </view>

              <view class="experience-info">
                <text class="experience-text"
                  >{{ matchmaker.years }}年经验</text
                >
                <text class="separator">·</text>
                <text class="area-text">{{ matchmaker.area }}</text>
              </view>

              <view class="success-info">
                <text class="success-text"
                  >成功配对 {{ matchmaker.successCount }} 对</text
                >
              </view>
            </view>
          </view>

          <!-- 服务标签 -->
          <view class="services-section">
            <view class="services-title">
              <text>专业服务</text>
            </view>
            <view class="services-tags">
              <view
                v-for="(service, index) in matchmaker.services.slice(0, 3)"
                :key="index"
                class="service-tag"
              >
                {{ service }}
              </view>
            </view>
          </view>

          <!-- 简介 -->
          <view v-if="matchmaker.introduction" class="introduction-section">
            <text class="introduction-text">{{ matchmaker.introduction }}</text>
          </view>

          <!-- 底部操作 -->
          <view class="matchmaker-footer">
            <view class="price-info">
              <text class="price-label">咨询费用</text>
              <text class="price-value">¥{{ matchmaker.consultPrice }}/次</text>
            </view>

            <view class="action-buttons">
              <view
                class="action-btn secondary"
                @click.stop="contactMatchmaker(matchmaker)"
              >
                <text class="i-carbon-chat"></text>
                <text>咨询</text>
              </view>
              <view
                class="action-btn primary"
                @click.stop="bookMatchmaker(matchmaker)"
              >
                <text class="i-carbon-calendar"></text>
                <text>预约</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";

// 响应式数据
const activeFilter = ref("all");
const refreshing = ref(false);

// 筛选标签
const filterTabs = ref([
  { label: "全部", value: "all" },
  { label: "官方认证", value: "official" },
  { label: "在线", value: "online" },
  { label: "高评分", value: "high_rating" },
]);

// 模拟红娘数据
const matchmakersList = ref([
  {
    id: "6001",
    name: "王阳阳",
    avatar: "https://picsum.photos/300/300?random=40",
    rating: 4.9,
    years: 8,
    area: "市中心",
    successCount: 128,
    isOfficial: true,
    isOnline: true,
    consultPrice: 199,
    services: ["精准匹配", "家庭财务分析", "性格测试", "婚前咨询"],
    introduction: "专业婚恋顾问，擅长高端人群匹配，成功率高达85%。",
    specialties: ["高端匹配", "心理咨询"],
  },
  {
    id: "6002",
    name: "张宝弘",
    avatar: "https://picsum.photos/300/300?random=41",
    rating: 4.7,
    years: 12,
    area: "西郊",
    successCount: 156,
    isOfficial: true,
    isOnline: false,
    consultPrice: 299,
    services: ["一对一约见", "结婚证书核实", "家庭调解", "婚礼策划"],
    introduction: "资深红娘，从业12年，专注于传统婚恋观念匹配。",
    specialties: ["传统匹配", "家庭咨询"],
  },
  {
    id: "6003",
    name: "李春花",
    avatar: "https://picsum.photos/300/300?random=42",
    rating: 4.5,
    years: 5,
    area: "北区",
    successCount: 87,
    isOfficial: false,
    isOnline: true,
    consultPrice: 99,
    services: ["儿女相亲服务", "婚姻频道反馈", "恋爱心理诊断"],
    introduction: "年轻红娘，了解现代年轻人的恋爱观念。",
    specialties: ["年轻群体", "现代恋爱"],
  },
  {
    id: "6004",
    name: "陈志强",
    avatar: "https://picsum.photos/300/300?random=43",
    rating: 4.8,
    years: 10,
    area: "南区",
    successCount: 132,
    isOfficial: true,
    isOnline: true,
    consultPrice: 399,
    services: ["高端客户配对", "背景调查", "专属红娘", "VIP服务"],
    introduction: "高端红娘，专为成功人士提供专属婚恋服务。",
    specialties: ["高端服务", "VIP定制"],
  },
]);

// 计算属性
const filteredMatchmakers = computed(() => {
  let filtered = matchmakersList.value;

  switch (activeFilter.value) {
    case "official":
      filtered = filtered.filter((m) => m.isOfficial);
      break;
    case "online":
      filtered = filtered.filter((m) => m.isOnline);
      break;
    case "high_rating":
      filtered = filtered.filter((m) => m.rating >= 4.7);
      break;
    default:
      break;
  }

  return filtered.sort((a, b) => {
    // 官方认证优先，然后按评分排序
    if (a.isOfficial && !b.isOfficial) return -1;
    if (!a.isOfficial && b.isOfficial) return 1;
    return b.rating - a.rating;
  });
});

// 方法
const setFilter = (filter: string) => {
  activeFilter.value = filter;
};

const onRefresh = () => {
  refreshing.value = true;
  setTimeout(() => {
    refreshing.value = false;
  }, 1000);
};

const showSearch = () => {
  uni.navigateTo({
    url: "/pages/dating/matchmaker/search",
  });
};

const viewMatchmakerDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/dating/matchmaker/detail?id=${id}`,
  });
};

const contactMatchmaker = (matchmaker: any) => {
  uni.navigateTo({
    url: `/pages/dating/chat/conversation?userId=${matchmaker.id}&name=${matchmaker.name}&type=matchmaker`,
  });
};

const bookMatchmaker = (matchmaker: any) => {
  uni.showModal({
    title: "预约红娘",
    content: `确定要预约${matchmaker.name}红娘吗？咨询费用：¥${matchmaker.consultPrice}/次`,
    success: (res) => {
      if (res.confirm) {
        uni.navigateTo({
          url: `/pages/dating/matchmaker/booking?id=${matchmaker.id}`,
        });
      }
    },
  });
};

onMounted(() => {
  // 初始化数据
});
</script>

<style lang="scss" scoped>
.matchmaker-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.matchmaker-header {
  background: white;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
}

.action-icon {
  font-size: 44rpx;
  color: #666;
}

.filter-tabs {
  background: white;
  display: flex;
  padding: 20rpx 30rpx;
  gap: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-tab {
  color: #666;
  font-size: 28rpx;
  padding: 15rpx 0;
  position: relative;
  transition: all 0.3s;

  &.active {
    color: #ff5778;
    font-weight: bold;

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 40rpx;
      height: 4rpx;
      background: #ff5778;
      border-radius: 2rpx;
    }
  }
}

.matchmaker-list {
  flex: 1;
  padding: 20rpx 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 120rpx;
  color: #ddd;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
}

.matchmakers-container {
  padding: 0 30rpx;
}

.matchmaker-card {
  background: white;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
  padding: 25rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s;

  &:active {
    transform: scale(0.98);
  }
}

.matchmaker-header {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.avatar-section {
  position: relative;
  flex-shrink: 0;
}

.matchmaker-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}

.online-indicator {
  position: absolute;
  bottom: 5rpx;
  right: 5rpx;
  width: 20rpx;
  height: 20rpx;
  background: #4cd964;
  border: 3rpx solid white;
  border-radius: 50%;
}

.official-badge {
  position: absolute;
  top: -5rpx;
  right: -5rpx;
  width: 32rpx;
  height: 32rpx;
  background: #ff5778;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18rpx;
}

.matchmaker-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.name-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.matchmaker-name {
  font-size: 32rpx;
  font-weight: bold;
}

.rating {
  display: flex;
  align-items: center;
  gap: 5rpx;
}

.star-icon {
  font-size: 24rpx;
  color: #ffd700;
}

.rating-text {
  font-size: 26rpx;
  color: #666;
}

.experience-info {
  display: flex;
  align-items: center;
  gap: 10rpx;
  font-size: 24rpx;
  color: #666;
}

.separator {
  color: #ddd;
}

.success-info {
  font-size: 24rpx;
  color: #ff5778;
}

.services-section {
  margin-bottom: 15rpx;
}

.services-title {
  font-size: 26rpx;
  margin-bottom: 10rpx;
}

.services-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.service-tag {
  background: #f0f0f0;
  color: #666;
  font-size: 22rpx;
  padding: 6rpx 15rpx;
  border-radius: 15rpx;
}

.introduction-section {
  margin-bottom: 20rpx;
}

.introduction-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.matchmaker-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.price-info {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.price-label {
  font-size: 22rpx;
  color: #999;
}

.price-value {
  font-size: 28rpx;
  color: #ff5778;
  font-weight: bold;
}

.action-buttons {
  display: flex;
  gap: 15rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  transition: all 0.3s;

  &.primary {
    background: #ff5778;
    color: white;
  }

  &.secondary {
    background: #f5f5f5;
    color: #666;
  }
}
</style>
