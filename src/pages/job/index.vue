<template>
  <view class="job-router-container">
    <view class="loading-container flex-y-center">
      <tui-loading />
      <text class="loading-text">正在为您准备专属页面...</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { useJobStore } from "@/stores";

const jobStore = useJobStore();

onShow(() => {
  // 先从本地存储恢复角色状态
  jobStore.restoreRole();
  
  // 获取用户角色，默认为求职者
  const userRole = jobStore.currentRole || "jobseeker";
  
  console.log("当前用户角色:", userRole);

  // 根据角色跳转到对应页面
  const routeMap = {
    jobseeker: "/pages/job/jobseeker/index",
    recruiter: "/pages/job/recruiter/index",
  };

  // 使用 reLaunch 替换当前页面，避免回退问题
  uni.reLaunch({
    url: routeMap[userRole],
    animationType: "slide-in-right",
    animationDuration: 300,
  });
});
</script>

<style lang="scss" scoped>
.job-router-container {
  height: 100vh;
  background: linear-gradient(
    135deg,
    var(--primary-light) 0%,
    var(--primary) 100%
  );

  .loading-container {
    height: 100%;

    .loading-text {
      margin-top: 40rpx;
      color: var(--primary);
      font-size: 28rpx;
    }
  }
}
</style>
