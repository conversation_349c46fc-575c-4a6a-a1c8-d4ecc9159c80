<template>
  <view class="container pb-100rpx">
    <uni-nav-bar
      fixed
      statusBar="true"
      :border="false"
      title=""
      left-icon="back"
      :backgroundColor="scrollTop > 50 ? '#ffffff' : 'transparent'"
      @clickLeft="goBack"
    />

    <!-- 搜索框和角色切换按钮 -->
    <view class="search-header p-20rpx mb-2">
      <view class="search-box">
        <view class="search-input-box">
          <text class="i-solar:minimalistic-magnifer-linear search-icon"></text>
          <input
            class="search-input"
            type="text"
            placeholder="搜索职位、公司、技能"
            v-model="searchKeyword"
            @confirm="handleSearch"
          />
        </view>
        <!-- 角色切换按钮 -->
        <view class="role-switch-container">
          <view class="role-switch-btn" @tap="switchToRecruiter">
            <text>我要招人</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 重要功能入口 -->
    <view class="job-tools p-20rpx mb-2">
      <view class="grid grid-cols-5">
        <view
          v-for="(tool, index) in jobSeekerTools"
          :key="index"
          class="job-tool-item flex flex-col items-center"
          @tap="handleToolClick(tool.id)"
        >
          <view
            class="tool-icon-circle mb-10rpx"
            :class="`bg-gradient-to-br ${tool.gradient}`"
          >
            <text :class="tool.icon"></text>
          </view>
          <text class="text-26rpx text-secondary">{{ tool.name }}</text>
        </view>
      </view>
    </view>

    <!-- JobseekerView 组件内容 -->
    <JobseekerView @openFilterPopup="openFilterPopup" />

    <!-- 筛选弹出层 -->
    <tui-bottom-popup
      :show="showFilterPopup"
      :height="1300"
      :isSafeArea="true"
      :zIndex="1002"
      :maskZIndex="1001"
      backgroundColor="#ffffff"
      @close="showFilterPopup = false"
    >
      <view class="filter-popup-content">
        <view
          class="filter-popup-header flex justify-between items-center px-30rpx py-20rpx"
        >
          <text class="text-32rpx font-bold">筛选</text>
          <text
            class="close-btn i-carbon-close"
            @tap="showFilterPopup = false"
          ></text>
        </view>

        <!-- 筛选选项 -->
        <scroll-view scroll-y enable-flex class="filter-options-scroll">
          <filter-option
            title="经验要求"
            type="experience"
            :options="experienceOptions"
            @option-change="handleOptionChange"
          />

          <filter-option
            title="职位类型"
            type="jobType"
            :options="jobTypeOptions"
            @option-change="handleOptionChange"
          />

          <filter-option
            title="学历要求"
            type="education"
            :options="educationOptions"
            @option-change="handleOptionChange"
          />

          <filter-option
            title="薪资待遇"
            type="salary"
            :options="salaryOptions"
            @option-change="handleOptionChange"
          />

          <filter-option
            title="公司规模"
            type="companySize"
            :options="companySizeOptions"
            @option-change="handleOptionChange"
          />

          <filter-option
            title="福利待遇"
            type="welfare"
            :options="welfareOptions"
            :isLastSection="true"
            @option-change="handleOptionChange"
          />
        </scroll-view>

        <!-- 操作按钮 -->
        <view class="filter-actions flex px-30rpx py-20rpx">
          <view
            class="reset-btn flex-1 text-center py-20rpx mr-20rpx"
            @tap="resetFilter"
            >清除</view
          >
          <view
            class="confirm-btn flex-1 text-center py-20rpx"
            @tap="confirmFilter"
            >确定</view
          >
        </view>
      </view>
    </tui-bottom-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import tuiBottomPopup from "@/components/thorui/tui-bottom-popup/tui-bottom-popup.vue";
import FilterOption from "@/components/home/<USER>";
import JobseekerView from "../components/JobseekerView.vue";
import { useJobStore } from "@/stores/job";

const jobStore = useJobStore();
const scrollTop = ref(0);
const searchKeyword = ref("");
const showFilterPopup = ref(false);

// 页面加载时恢复角色状态
onMounted(() => {
  jobStore.restoreRole();
});

const goBack = () => {
  uni.navigateBack();
};

onPageScroll((e) => {
  scrollTop.value = e.scrollTop;
});

// 求职者功能工具 - 统一使用渐变色彩方案
const jobSeekerTools = [
  {
    id: "resume",
    name: "在线简历",
    icon: "i-carbon-document",
    gradient: "from-blue-400 to-blue-600",
  },
  {
    id: "part-time",
    name: "兼职信息",
    icon: "i-carbon-time",
    gradient: "from-green-400 to-green-600",
  },
  {
    id: "factory-work",
    name: "普工招聘",
    icon: "i-carbon-tools",
    gradient: "from-orange-400 to-orange-600",
  },
  {
    id: "temp-work",
    name: "临时工",
    icon: "i-carbon-calendar",
    gradient: "from-purple-400 to-purple-600",
  },
  {
    id: "job-fair",
    name: "线下招聘会",
    icon: "i-carbon-event",
    gradient: "from-pink-400 to-pink-600",
  },
];

// 切换到招聘者角色
const switchToRecruiter = () => {
  uni.showLoading({
    title: "切换中...",
  });
  setTimeout(() => {
    jobStore.switchRole("recruiter");
    uni.redirectTo({
      url: "/pages/job/recruiter/index",
      animationType: "zoom-out",
      animationDuration: 0,
    });
    uni.hideLoading();
  }, 300);
};

// 处理工具点击
const handleToolClick = (toolId) => {
  console.log("clicked tool:", toolId);
  if (toolId === "resume") {
    uni.navigateTo({
      url: `/pages/job/resume/resume`,
    });
  } else if (toolId === "part-time") {
    uni.navigateTo({
      url: `/pages/job/parttime`,
    });
  } else if (toolId === "factory-work") {
    uni.navigateTo({
      url: `/pages/job/factory-work`,
    });
  } else if (toolId === "temp-work") {
    uni.navigateTo({
      url: `/pages/job/temp-work`,
    });
  } else if (toolId === "job-fair") {
    uni.navigateTo({
      url: `/pages/job/job-fair`,
    });
  } else {
    uni.navigateTo({
      url: `/pages/job/${toolId}`,
    });
  }
};

// 处理搜索
const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    uni.showToast({
      title: "请输入搜索关键词",
      icon: "none",
    });
    return;
  }

  console.log("搜索关键词:", searchKeyword.value);
  uni.navigateTo({
    url: `/pages/job/search?keyword=${encodeURIComponent(searchKeyword.value)}`,
  });
};

// 筛选选项数据
const experienceOptions = [
  { name: "全部", active: true },
  { name: "1年以内", active: false },
  { name: "1-3年", active: false },
  { name: "3-5年", active: false },
  { name: "5-10年", active: false },
  { name: "10年以上", active: false },
  { name: "应届生", active: false },
  { name: "在校生", active: false },
];

const jobTypeOptions = [
  { name: "全部", active: true },
  { name: "全职", active: false },
  { name: "兼职", active: false },
  { name: "实习", active: false },
  { name: "临时工", active: false },
];

const educationOptions = [
  { name: "不限", active: true },
  { name: "高中", active: false },
  { name: "中专", active: false },
  { name: "大专", active: false },
  { name: "本科", active: false },
  { name: "硕士及以上", active: false },
];

const welfareOptions = [
  { name: "五险一金", active: false },
  { name: "包吃", active: false },
  { name: "包住", active: false },
  { name: "年终奖", active: false },
  { name: "加班费", active: false },
  { name: "定期体检", active: false },
  { name: "带薪年假", active: false },
  { name: "交通补贴", active: false },
  { name: "餐补", active: false },
  { name: "通讯补贴", active: false },
  { name: "医疗保险", active: false },
  { name: "社保", active: false },
];

const salaryOptions = [
  { name: "全部", active: true },
  { name: "15K以下", active: false },
  { name: "15-25K", active: false },
  { name: "25-35K", active: false },
  { name: "35-45K", active: false },
  { name: "45K以上", active: false },
];

const companySizeOptions = [
  { name: "全部", active: true },
  { name: "0-20人", active: false },
  { name: "20-99人", active: false },
  { name: "100-499人", active: false },
  { name: "500-999人", active: false },
  { name: "1000-9999人", active: false },
  { name: "10000人以上", active: false },
];

// 筛选相关方法
const toggleFilterOption = (type, index) => {
  let options;

  if (type === "experience") {
    options = experienceOptions;
  } else if (type === "jobType") {
    options = jobTypeOptions;
  } else if (type === "education") {
    options = educationOptions;
  } else if (type === "salary") {
    options = salaryOptions;
  } else if (type === "companySize") {
    options = companySizeOptions;
  } else if (type === "welfare") {
    options = welfareOptions;
    options[index].active = !options[index].active;
    return;
  }

  if (index === 0) {
    options.forEach((opt, idx) => {
      opt.active = idx === 0;
    });
  } else {
    options[0].active = false;
    options[index].active = !options[index].active;

    if (!options.some((opt) => opt.active)) {
      options[0].active = true;
    }
  }
};

const resetFilter = () => {
  experienceOptions.forEach((opt, idx) => {
    opt.active = idx === 0;
  });

  jobTypeOptions.forEach((opt, idx) => {
    opt.active = idx === 0;
  });

  educationOptions.forEach((opt, idx) => {
    opt.active = idx === 0;
  });

  salaryOptions.forEach((opt, idx) => {
    opt.active = idx === 0;
  });

  companySizeOptions.forEach((opt, idx) => {
    opt.active = idx === 0;
  });

  welfareOptions.forEach((opt) => {
    opt.active = false;
  });
};

const openFilterPopup = () => {
  showFilterPopup.value = true;
};

const handleOptionChange = ({ type, index }) => {
  toggleFilterOption(type, index);
};

const confirmFilter = () => {
  showFilterPopup.value = false;
};
</script>

<style lang="scss" scoped>
.container {
  background-color: var(--bg-page);
  background-image: linear-gradient(
      to bottom,
      #e3eeff 0%,
      #eef3ff 10%,
      var(--bg-page) 20%
    ),
    var(--bg-page);

  // 搜索框样式
  .search-header {
    .search-box {
      display: flex;
      align-items: center;
      gap: 20rpx;
      width: 100%;

      .search-input-box {
        flex: 1;
        display: flex;
        align-items: center;
        background-color: #fff;
        border-radius: 36rpx;
        padding: 0 24rpx;
        margin-left: 22rpx;
        height: 72rpx;
        border: 2rpx solid var(--primary-400);

        .search-icon {
          font-size: 32rpx;
          color: var(--text-info);
          margin-right: 10rpx;
        }

        .search-input {
          flex: 1;
          height: 80rpx;
          font-size: 28rpx;
          color: #333;
        }
      }

      .role-switch-container {
        .role-switch-btn {
          padding: 16rpx 24rpx;
          background: linear-gradient(
            90deg,
            var(--primary-400),
            var(--primary)
          );
          color: #fff;
          border-radius: 36rpx;
          font-size: 26rpx;
          font-weight: 500;
          box-shadow: 0 4rpx 8rpx rgba(var(--primary), 0.3);
          transition: all 0.2s;
          white-space: nowrap;

          &:active {
            transform: scale(0.95);
            opacity: 0.9;
          }
        }
      }
    }
  }

  .tool-icon-circle {
    width: 88rpx;
    height: 88rpx;
    border-radius: 24rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #ffffff;
    font-size: 36rpx;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.1);
      opacity: 0;
      transition: opacity 0.3s;
    }

    &:active {
      transform: translateY(2rpx) scale(0.95);
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);

      &::before {
        opacity: 1;
      }
    }
  }

  /* 筛选弹出层样式 */
  .filter-popup-content {
    background-color: $bg-card;
    height: 100%;
    display: flex;
    flex-direction: column;
    max-height: 85vh;
    height: 100%;
    padding-bottom: 100rpx;
  }

  .filter-popup-header {
    border-bottom: 1rpx solid $border-color;
  }

  .close-btn {
    font-size: 40rpx;
    color: $text-grey;
  }

  .filter-options-scroll {
    flex: 1;
    height: calc(100% - 160rpx);
  }

  .filter-actions {
    padding: 30rpx;
    border-top: 1rpx solid $border-color;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: $bg-card;
    z-index: 9;
  }

  .reset-btn {
    border: 1rpx solid $border-color;
    border-radius: 8rpx;
    font-size: 30rpx;
    color: $text-secondary;
    transition: all 0.2s;

    &:active {
      background-color: #f5f5f5;
    }
  }

  .confirm-btn {
    background-color: $primary;
    color: $text-inverse;
    border-radius: 8rpx;
    font-size: 30rpx;
    font-weight: 500;
    transition: all 0.2s;

    &:active {
      opacity: 0.8;
    }
  }
}
</style>
