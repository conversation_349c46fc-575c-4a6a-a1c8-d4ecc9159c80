<template>
  <view class="edit-container safe-area-inset-bottom">
    <!-- 导航栏 -->
    <uni-nav-bar
      :fixed="true"
      :border="false"
      status-bar
      :title="getPageTitle"
      left-icon="left"
      @clickLeft="goBack"
    />

    <!-- 编辑表单 -->
    <scroll-view scroll-y enable-flex class="edit-content">
      <!-- 个人信息编辑 -->
      <view v-if="section === 'personal'" class="edit-form">
        <view class="section-title">基本信息</view>

        <view class="form-group">
          <view class="form-label required">
            <text>姓名</text>
            <text class="required-mark">*</text>
          </view>
          <input
            class="form-input"
            type="text"
            v-model="formData.name"
            placeholder="请输入姓名"
          />
        </view>

        <view class="form-group">
          <view class="form-label required">
            <text>性别</text>
            <text class="required-mark">*</text>
          </view>
          <view class="radio-group">
            <view
              class="radio-item"
              :class="{ active: formData.gender === '男' }"
              @tap="formData.gender = '男'"
            >
              <text
                class="radio-icon i-carbon-radio-button"
                v-if="formData.gender !== '男'"
              ></text>
              <text
                class="radio-icon i-carbon-radio-button-checked"
                v-else
              ></text>
              <text>男</text>
            </view>
            <view
              class="radio-item"
              :class="{ active: formData.gender === '女' }"
              @tap="formData.gender = '女'"
            >
              <text
                class="radio-icon i-carbon-radio-button"
                v-if="formData.gender !== '女'"
              ></text>
              <text
                class="radio-icon i-carbon-radio-button-checked"
                v-else
              ></text>
              <text>女</text>
            </view>
          </view>
        </view>

        <view class="form-group">
          <view class="form-label">
            <text>生日</text>
          </view>
          <picker
            mode="date"
            :value="formData.birthday"
            start="1950-01-01"
            end="2010-12-31"
            @change="onBirthdayChange"
          >
            <view class="picker-view">
              <text>{{ formData.birthday || "请选择生日" }}</text>
              <text class="i-carbon-chevron-down"></text>
            </view>
          </picker>
        </view>

        <view class="section-title">联系方式</view>

        <view class="form-group">
          <view class="form-label required">
            <text>手机号码</text>
            <text class="required-mark">*</text>
          </view>
          <input
            class="form-input"
            type="number"
            v-model="formData.phone"
            placeholder="请输入手机号码"
            maxlength="11"
          />
        </view>

        <view class="section-title">求职信息</view>

        <view class="form-group">
          <view class="form-label">
            <text>求职意向</text>
          </view>
          <input
            class="form-input"
            type="text"
            v-model="formData.jobIntent"
            placeholder="请输入求职意向"
          />
        </view>

        <view class="form-group">
          <view class="form-label">
            <text>工作经验</text>
          </view>
          <picker
            mode="selector"
            :range="experienceOptions"
            @change="onExperienceChange"
          >
            <view class="picker-view">
              <text>{{ formData.experience || "请选择工作经验" }}</text>
              <text class="i-carbon-chevron-down"></text>
            </view>
          </picker>
        </view>

        <view class="form-group">
          <view class="form-label">
            <text>最高学历</text>
          </view>
          <picker
            mode="selector"
            :range="educationOptions"
            @change="onEducationChange"
          >
            <view class="picker-view">
              <text>{{ formData.education || "请选择最高学历" }}</text>
              <text class="i-carbon-chevron-down"></text>
            </view>
          </picker>
        </view>
      </view>

      <!-- 教育经历编辑 -->
      <view v-else-if="section === 'education'" class="edit-form">
        <view class="section-title">教育信息</view>

        <view class="form-group">
          <view class="form-label">
            <text>学校名称</text>
          </view>
          <input
            class="form-input"
            type="text"
            v-model="formData.school"
            placeholder="请输入学校名称"
          />
        </view>

        <view class="form-group">
          <view class="form-label">
            <text>专业</text>
          </view>
          <input
            class="form-input"
            type="text"
            v-model="formData.major"
            placeholder="请输入专业名称"
          />
        </view>

        <view class="form-group">
          <view class="form-label">
            <text>学历</text>
          </view>
          <picker
            mode="selector"
            :range="educationOptions"
            @change="onDegreeChange"
          >
            <view class="picker-view">
              <text>{{ formData.degree || "请选择学历" }}</text>
              <text class="i-carbon-chevron-down"></text>
            </view>
          </picker>
        </view>

        <view class="form-group">
          <view class="form-label">
            <text class="i-carbon-calendar" style="margin-right: 8rpx"></text>
            <text>起止时间</text>
          </view>
          <input
            class="form-input"
            type="text"
            v-model="formData.time"
            placeholder="例如：2016.09-2020.07"
          />
        </view>
      </view>

      <!-- 工作经验编辑 -->
      <view v-else-if="section === 'experience'" class="edit-form">
        <view class="section-title">工作经历</view>

        <view class="form-group">
          <view class="form-label">
            <text>公司名称</text>
          </view>
          <input
            class="form-input"
            type="text"
            v-model="formData.company"
            placeholder="请输入公司名称"
          />
        </view>

        <view class="form-group">
          <view class="form-label">
            <text>职位名称</text>
          </view>
          <input
            class="form-input"
            type="text"
            v-model="formData.title"
            placeholder="请输入职位名称"
          />
        </view>

        <view class="form-group">
          <view class="form-label">
            <text>起止时间</text>
          </view>
          <input
            class="form-input"
            type="text"
            v-model="formData.time"
            placeholder="例如：2020.07-至今"
          />
        </view>

        <view class="form-group">
          <view class="form-label">
            <text>工作内容</text>
          </view>
          <view class="work-items">
            <view
              v-for="(responsibility, index) in formData.responsibilities"
              :key="index"
              class="work-item-edit"
            >
              <textarea
                class="form-textarea"
                v-model="formData.responsibilities[index]"
                :placeholder="
                  responsibility ? '编辑工作内容' : '请输入工作内容'
                "
              />
              <view class="delete-btn" @tap="deleteWorkItem(index)">
                <text class="i-carbon-trash-can"></text>
              </view>
            </view>
            <view class="add-work-item" @tap="addWorkItem">
              <text class="i-carbon-add"></text>
              <text>添加工作内容</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 技能编辑 -->
      <view v-else-if="section === 'skills'" class="edit-form">
        <view class="section-title">专业技能</view>

        <view class="form-group">
          <view class="form-label">
            <text>技能标签</text>
          </view>
          <view class="skills-edit">
            <view
              v-for="(skill, index) in formData.skills"
              :key="index"
              class="skill-tag-edit"
            >
              <text>{{ skill }}</text>
              <text class="delete-icon" @tap="deleteSkill(index)">×</text>
            </view>
            <view class="add-skill" @tap="showAddSkill = true">
              <text class="i-carbon-add"></text>
              <text>添加技能</text>
            </view>
          </view>
        </view>

        <view class="form-group" v-if="showAddSkill">
          <input
            class="form-input"
            type="text"
            v-model="newSkill"
            placeholder="请输入技能名称"
          />
          <view class="skill-actions">
            <view class="cancel-btn" @tap="showAddSkill = false">取消</view>
            <view class="confirm-btn" @tap="addSkill">确认</view>
          </view>
        </view>

        <view class="form-group">
          <view class="form-label">技能描述</view>
          <textarea
            class="form-textarea"
            v-model="formData.skillDescription"
            placeholder="请描述你的技能和专长"
          />
        </view>
      </view>

      <!-- 自我评价编辑 -->
      <view v-else-if="section === 'evaluation'" class="edit-form">
        <view class="section-title">自我评价</view>

        <view class="form-group">
          <view class="form-label">
            <text>个人介绍</text>
          </view>
          <textarea
            class="form-textarea"
            v-model="formData.selfEvaluation"
            placeholder="请输入自我评价"
          />
        </view>
      </view>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="bottom-actions">
      <view class="action-btn outline-btn" @tap="goBack">
        <text>取消</text>
      </view>
      <view class="action-btn primary-btn" @tap="saveData">
        <text>保存</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
// 获取页面参数
const section = ref("");
const action = ref("");
const index = ref(-1);

// 表单数据
const formData = reactive({
  name: "",
  gender: "",
  birthday: "",
  phone: "",
  email: "",
  jobIntent: "",
  experience: "",
  education: "",
  educations: [],
  experiences: [],
  skills: [],
  skillDescription: "",
  selfEvaluation: "",
  responsibilities: [],
  school: "",
  major: "",
  degree: "",
  time: "",
  company: "",
  title: "",
});
const showAddSkill = ref(false);
const newSkill = ref("");

// 选项数据
const experienceOptions = [
  "应届毕业生",
  "1年以下",
  "1-3年",
  "3-5年",
  "5-10年",
  "10年以上",
];
const educationOptions = ["高中", "大专", "本科", "硕士", "博士"];

// 页面标题
const getPageTitle = computed(() => {
  if (section.value === "personal") return "编辑个人信息";
  if (section.value === "education") {
    return action.value === "add" ? "添加教育经历" : "编辑教育经历";
  }
  if (section.value === "experience") {
    return action.value === "add" ? "添加工作经验" : "编辑工作经验";
  }
  if (section.value === "skills") return "编辑专业技能";
  if (section.value === "evaluation") return "编辑自我评价";
  return "编辑简历";
});

// 加载页面参数
onLoad((options) => {
  if (options.section) {
    section.value = options.section;
  }
  if (options.action) {
    action.value = options.action;
  }
  if (options.index) {
    index.value = parseInt(options.index);
  }

  // 初始化表单数据
  initFormData();
});

// 初始化表单数据
const initFormData = () => {
  // 这里应该从全局状态或本地存储获取简历数据
  // 为了演示，使用模拟数据
  const resumeData = {
    name: "张小明",
    gender: "男",
    birthday: "1995-01-15",
    phone: "13812345678",
    email: "<EMAIL>",
    jobIntent: "前端开发工程师",
    experience: "3年",
    education: "本科",
    educations: [
      {
        school: "浙江大学",
        major: "计算机科学与技术",
        degree: "本科",
        time: "2016.09-2020.07",
      },
    ],
    experiences: [
      {
        company: "杭州某科技有限公司",
        title: "前端开发工程师",
        time: "2020.07-至今",
        responsibilities: [
          "负责公司电商平台的前端页面开发与维护",
          "参与项目需求分析，技术选型，架构设计",
        ],
      },
    ],
    skills: ["HTML/CSS", "JavaScript", "Vue.js", "uni-app"],
    skillDescription:
      "熟练掌握前端开发技术栈，包括HTML/CSS/JavaScript，熟悉Vue.js框架及其生态。",
    selfEvaluation:
      "性格开朗，责任心强，善于团队合作。具有良好的沟通能力和解决问题的能力。",
  };

  // 根据不同的编辑部分初始化表单数据
  if (section.value === "personal") {
    Object.assign(formData, {
      name: resumeData.name,
      gender: resumeData.gender,
      birthday: resumeData.birthday,
      phone: resumeData.phone,
      email: resumeData.email,
      jobIntent: resumeData.jobIntent,
      experience: resumeData.experience,
      education: resumeData.education,
    });
  } else if (section.value === "education") {
    if (action.value === "add") {
      Object.assign(formData, {
        school: "",
        major: "",
        degree: "",
        time: "",
      });
    } else {
      // 编辑现有教育经历
      const eduIndex = index.value >= 0 ? index.value : 0;
      Object.assign(formData, { ...resumeData.educations[eduIndex] });
    }
  } else if (section.value === "experience") {
    if (action.value === "add") {
      Object.assign(formData, {
        company: "",
        title: "",
        time: "",
        responsibilities: [""],
      });
    } else {
      // 编辑现有工作经验
      const expIndex = index.value >= 0 ? index.value : 0;
      Object.assign(formData, { ...resumeData.experiences[expIndex] });
    }
  } else if (section.value === "skills") {
    Object.assign(formData, {
      skills: [...resumeData.skills],
      skillDescription: resumeData.skillDescription,
    });
  } else if (section.value === "evaluation") {
    Object.assign(formData, {
      selfEvaluation: resumeData.selfEvaluation,
    });
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 保存数据
const saveData = () => {
  // 验证必填字段
  if (section.value === "personal") {
    if (!formData.name?.trim()) {
      uni.showToast({
        title: "请输入姓名",
        icon: "none",
      });
      return;
    }

    if (!formData.gender) {
      uni.showToast({
        title: "请选择性别",
        icon: "none",
      });
      return;
    }

    if (!formData.phone?.trim()) {
      uni.showToast({
        title: "请输入手机号码",
        icon: "none",
      });
      return;
    }

    // 验证手机号格式
    const phoneReg = /^1[3-9]\d{9}$/;
    if (!phoneReg.test(formData.phone)) {
      uni.showToast({
        title: "请输入正确的手机号码",
        icon: "none",
      });
      return;
    }
  }

  // 这里应该将数据保存到全局状态或本地存储
  // 为了演示，只显示保存成功提示
  uni.showToast({
    title: "保存成功",
    icon: "success",
    success: () => {
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    },
  });
};

// 选择器事件处理
const onExperienceChange = (e: { detail: { value: number } }) => {
  formData.experience = experienceOptions[e.detail.value];
};

const onEducationChange = (e: { detail: { value: number } }) => {
  formData.education = educationOptions[e.detail.value];
};

const onBirthdayChange = (e: { detail: { value: string } }) => {
  formData.birthday = e.detail.value;
};

const onDegreeChange = (e: { detail: { value: number } }) => {
  formData.degree = educationOptions[e.detail.value];
};

// 工作内容相关方法
const addWorkItem = () => {
  formData.responsibilities.push("");
};

const deleteWorkItem = (index: number) => {
  if (formData.responsibilities.length > 1) {
    formData.responsibilities.splice(index, 1);
  } else {
    uni.showToast({
      title: "至少保留一项工作内容",
      icon: "none",
    });
  }
};

// 技能相关方法
const addSkill = () => {
  if (newSkill.value.trim()) {
    formData.skills.push(newSkill.value.trim());
    newSkill.value = "";
    showAddSkill.value = false;
  } else {
    uni.showToast({
      title: "技能名称不能为空",
      icon: "none",
    });
  }
};

const deleteSkill = (index: number) => {
  formData.skills.splice(index, 1);
};
</script>

<style lang="scss" scoped>
.edit-container {
  min-height: 100vh;
  width: 100%;
  background-color: var(--bg-page);
}

.edit-content {
  height: calc(100vh - 120rpx - env(safe-area-inset-bottom) - 44px);
  width: 100%;
  box-sizing: border-box;
  padding-bottom: env(safe-area-inset-bottom);
}

.edit-form {
  background-color: #fff;
  border-radius: $spacing-8;
  padding: $spacing-16;
  margin: $spacing-10;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: $text-base;
  padding-left: $spacing-8;
  border-left: 4rpx solid $primary;
  margin: $spacing-16 0;
  display: flex;
  align-items: center;
}

.form-group {
  margin-bottom: $spacing-16;
  width: 100%;
}

.form-label {
  margin-bottom: $spacing-8;
  font-size: 28rpx;
  color: $text-secondary;
  display: flex;
  align-items: center;

  &.required {
    .required-mark {
      color: $text-red;
      margin-left: 4rpx;
      font-weight: 600;
    }
  }
}

.form-input {
  width: 100%;
  height: 80rpx;
  background-color: var(--bg-input);
  border-radius: $spacing-8;
  padding: 0 $spacing-12;
  font-size: 28rpx;
  color: $text-base;
  box-sizing: border-box;
}

.form-textarea {
  width: 100%;
  background-color: var(--bg-input);
  border-radius: $spacing-8;
  padding: $spacing-12;
  font-size: 28rpx;
  color: $text-base;
  box-sizing: border-box;
  min-height: 180rpx;
  line-height: 1.5;
}

.picker-view {
  width: 100%;
  height: 80rpx;
  background-color: var(--bg-input);
  border-radius: $spacing-8;
  padding: 0 $spacing-12;
  font-size: 28rpx;
  color: $text-base;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.radio-group {
  display: flex;
  flex-wrap: wrap;
}

.radio-item {
  display: flex;
  align-items: center;
  margin-right: $spacing-16;
  padding: $spacing-8 $spacing-12;
  border-radius: $spacing-8;
  background-color: var(--bg-input);
  transition: all 0.2s;

  &.active {
    background-color: rgba($primary, 0.1);
    color: $primary;
  }
}

.radio-icon {
  margin-right: $spacing-4;
}

.skills-edit {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-10;
  margin-bottom: $spacing-10;
}

.skill-tag-edit {
  background-color: rgba($primary, 0.08);
  color: $primary;
  padding: $spacing-6 $spacing-12;
  border-radius: $spacing-16;
  font-size: 24rpx;
  display: flex;
  align-items: center;
}

.delete-icon {
  margin-left: $spacing-4;
  color: $text-grey;
  font-size: 20rpx;
  width: 30rpx;
  height: 30rpx;
  border-radius: 15rpx;
  background-color: rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-skill {
  display: flex;
  align-items: center;
  padding: $spacing-6 $spacing-12;
  border-radius: $spacing-16;
  font-size: 24rpx;
  color: $text-grey;
  background-color: var(--bg-input);
  border: 1rpx dashed $text-grey;
}

.skill-actions {
  margin-top: $spacing-8;
  display: flex;
  justify-content: flex-end;
}

.cancel-btn,
.confirm-btn {
  padding: $spacing-6 $spacing-16;
  border-radius: $spacing-8;
  font-size: 26rpx;
}

.cancel-btn {
  background-color: var(--bg-input);
  color: $text-secondary;
  margin-right: $spacing-10;
}

.confirm-btn {
  background-color: $primary;
  color: #fff;
}

.work-items {
  display: flex;
  flex-direction: column;
  gap: $spacing-10;
}

.work-item-edit {
  position: relative;
  width: 100%;
}

.delete-btn {
  position: absolute;
  right: $spacing-8;
  top: $spacing-8;
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  background-color: rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  color: $text-grey;
}

.add-work-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-8 0;
  background-color: rgba($primary, 0.05);
  border: 1rpx dashed rgba($primary, 0.3);
  color: $primary;
  border-radius: $spacing-4;
  font-size: 26rpx;
  margin-top: $spacing-10;

  text:first-child {
    margin-right: $spacing-4;
  }
}

// 底部按钮
.bottom-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  padding: $spacing-10;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding-bottom: calc($spacing-10 + env(safe-area-inset-bottom));
  z-index: 10;
  width: 100%;
  box-sizing: border-box;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  transition: all 0.2s;

  &:active {
    transform: scale(0.98);
  }
}

.outline-btn {
  margin-right: $spacing-10;
  border: 1rpx solid $border-color;
  color: $text-secondary;
}

.primary-btn {
  background: linear-gradient(to right, $primary, $primary-500);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba($primary, 0.3);

  &:active {
    box-shadow: 0 2rpx 8rpx rgba($primary, 0.2);
  }
}
</style>
