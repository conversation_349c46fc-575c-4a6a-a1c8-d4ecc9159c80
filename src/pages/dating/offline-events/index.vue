<template>
  <view class="offline-events-page">
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view class="filter-tabs">
        <view
          v-for="tab in eventTabs"
          :key="tab.value"
          class="filter-tab"
          :class="{ active: activeEventTab === tab.value }"
          @click="setEventTab(tab.value)"
        >
          {{ tab.label }}
        </view>
      </view>
    </view>

    <!-- 活动列表 -->
    <scroll-view
      scroll-y
      class="events-list"
      refresher-enabled
      @refresherrefresh="onRefresh"
      :refresher-triggered="refreshing"
    >
      <view v-if="filteredEvents.length === 0" class="empty-state">
        <text class="i-carbon-calendar empty-icon"></text>
        <text class="empty-text">暂无活动</text>
        <text class="empty-desc">敬请期待更多精彩活动</text>
      </view>

      <view v-else class="events-container">
        <view
          v-for="event in filteredEvents"
          :key="event.id"
          class="event-card"
          @click="viewEventDetail(event.id)"
        >
          <!-- 活动图片 -->
          <view class="event-image-container">
            <image :src="event.image" class="event-image" mode="aspectFill" />
            <view class="event-overlay">
              <view
                class="event-status"
                :class="{ 'status-active': !event.isFull }"
              >
                {{ getStatusText(event.status) }}
              </view>
              <view v-if="event.price === 0" class="free-badge"> 免费 </view>
            </view>
          </view>

          <!-- 活动信息 -->
          <view class="event-info">
            <view class="event-header">
              <text class="event-title">{{ event.title }}</text>
              <view class="event-organizer">
                <text class="organizer-text">{{ event.organizer }}</text>
              </view>
            </view>

            <view class="event-description">
              <text class="description-text">{{ event.description }}</text>
            </view>

            <view class="event-tags">
              <view
                v-for="(tag, index) in event.tags"
                :key="index"
                class="event-tag"
              >
                {{ tag }}
              </view>
            </view>

            <view class="event-details">
              <view class="detail-item">
                <text class="i-carbon-calendar detail-icon"></text>
                <text class="detail-text">{{ event.time }}</text>
              </view>
              <view class="detail-item">
                <text class="i-carbon-location detail-icon"></text>
                <text class="detail-text">{{ event.location }}</text>
              </view>
            </view>

            <!-- 参与人数和价格 -->
            <view class="event-footer">
              <view class="participant-info">
                <view class="gender-count">
                  <text class="male-count"
                    >♂ {{ event.maleCount }}/{{ event.maleLimit }}</text
                  >
                  <text class="female-count"
                    >♀ {{ event.femaleCount }}/{{ event.femaleLimit }}</text
                  >
                </view>
              </view>

              <view class="price-and-action">
                <view class="price-info">
                  <text v-if="event.price === 0" class="price-free">免费</text>
                  <view v-else class="price-container">
                    <text class="price-current">¥{{ event.price }}</text>
                    <text
                      v-if="event.originalPrice > event.price"
                      class="price-original"
                      >¥{{ event.originalPrice }}</text
                    >
                  </view>
                </view>

                <view
                  class="signup-btn"
                  :class="{ 'btn-disabled': event.isFull }"
                  @click.stop="signupEvent(event)"
                >
                  {{ event.isFull ? "已满员" : "立即报名" }}
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";

const eventTabs = ref([
  { label: "全部", value: "all" },
  { label: "本周", value: "week" },
  { label: "周末", value: "weekend" },
  { label: "免费", value: "free" },
  { label: "高端", value: "premium" },
]);

const activeEventTab = ref("all");
const refreshing = ref(false);

// 模拟活动数据
const eventsList = ref([
  {
    id: "5001",
    title: "周末游园对对碰相亲会",
    image: "https://picsum.photos/400/200?random=30",
    time: "2023-06-10 14:00-17:00",
    location: "星光公园中心广场",
    address: "星光公园中心广场",
    price: 68,
    originalPrice: 98,
    status: "active",
    isFull: false,
    maleCount: 12,
    femaleCount: 15,
    maleLimit: 20,
    femaleLimit: 20,
    type: "weekend",
    description: "在美丽的公园里，与志同道合的朋友一起度过愉快的周末时光。",
    organizer: "缘分红娘工作室",
    tags: ["户外", "轻松", "适合新手"],
  },
  {
    id: "5002",
    title: "咖啡小聚相亲小组",
    image: "https://picsum.photos/400/200?random=31",
    time: "2023-06-17 19:00-21:00",
    location: "市中心COCO咖啡厅",
    address: "市中心COCO咖啡厅",
    price: 88,
    originalPrice: 128,
    status: "active",
    isFull: false,
    maleCount: 8,
    femaleCount: 6,
    maleLimit: 8,
    femaleLimit: 8,
    type: "week",
    description: "在温馨的咖啡厅里，品味咖啡香，寻找心动的TA。",
    organizer: "都市情缘",
    tags: ["室内", "温馨", "小型聚会"],
  },
  {
    id: "5003",
    title: "掩面舞会相亲特别场",
    image: "https://picsum.photos/400/200?random=32",
    time: "2023-06-24 19:30-22:30",
    location: "晨曦酒店大安厅",
    address: "晨曦酒店大安厅",
    price: 128,
    originalPrice: 198,
    status: "full",
    isFull: true,
    maleCount: 25,
    femaleCount: 25,
    maleLimit: 25,
    femaleLimit: 25,
    type: "premium",
    description: "神秘的掩面舞会，让你在优雅的环境中邂逅真爱。",
    organizer: "高端婚恋服务",
    tags: ["高端", "神秘", "正式"],
  },
  {
    id: "5004",
    title: "免费公益相亲会",
    image: "https://picsum.photos/400/200?random=33",
    time: "2023-06-11 15:00-18:00",
    location: "社区文化中心",
    address: "社区文化中心",
    price: 0,
    originalPrice: 0,
    status: "active",
    isFull: false,
    maleCount: 18,
    femaleCount: 22,
    maleLimit: 30,
    femaleLimit: 30,
    type: "free",
    description: "社区公益活动，为单身朋友搭建交友平台。",
    organizer: "社区服务中心",
    tags: ["免费", "公益", "社区"],
  },
]);

// 计算属性
const filteredEvents = computed(() => {
  if (activeEventTab.value === "all") {
    return eventsList.value;
  }
  return eventsList.value.filter(
    (event) => event.type === activeEventTab.value
  );
});

// 方法
const setEventTab = (tab: string) => {
  activeEventTab.value = tab;
};

const onRefresh = () => {
  refreshing.value = true;
  setTimeout(() => {
    refreshing.value = false;
  }, 1000);
};

const viewEventDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/dating/offline-events/detail?id=${id}`,
  });
};

const getStatusText = (status: string) => {
  switch (status) {
    case "active":
      return "报名中";
    case "full":
      return "名额已满";
    case "ended":
      return "已结束";
    default:
      return "报名中";
  }
};

const signupEvent = (event: any) => {
  if (event.isFull) {
    uni.showToast({
      title: "活动已满员",
      icon: "none",
    });
    return;
  }

  uni.showModal({
    title: "确认报名",
    content: `确定要报名参加"${event.title}"吗？`,
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: "报名成功",
          icon: "success",
        });
      }
    },
  });
};

onMounted(() => {
  // 初始化数据
});
</script>

<style lang="scss" scoped>
.offline-events-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.filter-bar {
  background: white;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.filter-tabs {
  display: flex;
  gap: 30rpx;
}

.filter-tab {
  color: #666;
  font-size: 28rpx;
  padding: 15rpx 0;
  position: relative;
  transition: all 0.3s;

  &.active {
    color: #ff5778;
    font-weight: bold;

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 40rpx;
      height: 4rpx;
      background: #ff5778;
      border-radius: 2rpx;
    }
  }
}

.events-list {
  flex: 1;
  padding: 20rpx 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 120rpx;
  color: #ddd;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
}

.events-container {
  padding: 0 30rpx;
}

.event-card {
  background: white;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s;

  &:active {
    transform: scale(0.98);
  }
}

.event-image-container {
  position: relative;
  height: 200rpx;
}

.event-image {
  width: 100%;
  height: 100%;
}

.event-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), transparent);
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.event-status {
  background: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 22rpx;
  padding: 6rpx 15rpx;
  border-radius: 15rpx;

  &.status-active {
    background: rgba(255, 87, 120, 0.9);
  }
}

.free-badge {
  background: rgba(76, 217, 100, 0.9);
  color: white;
  font-size: 22rpx;
  padding: 6rpx 15rpx;
  border-radius: 15rpx;
}

.event-info {
  padding: 25rpx;
}

.event-header {
  margin-bottom: 15rpx;
}

.event-title {
  font-size: 32rpx;
  font-weight: bold;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: block;
}

.event-organizer {
  margin-bottom: 15rpx;
}

.organizer-text {
  font-size: 24rpx;
  color: #999;
}

.event-description {
  margin-bottom: 15rpx;
}

.description-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.event-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-bottom: 20rpx;
}

.event-tag {
  background: #f0f0f0;
  color: #666;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.event-details {
  margin-bottom: 20rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.detail-icon {
  font-size: 24rpx;
  color: #999;
  margin-right: 12rpx;
  width: 24rpx;
}

.detail-text {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}

.event-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.participant-info {
  flex: 1;
}

.gender-count {
  display: flex;
  gap: 20rpx;
}

.male-count,
.female-count {
  font-size: 24rpx;
  color: #666;
}

.male-count {
  color: #4a90e2;
}

.female-count {
  color: #ff5778;
}

.price-and-action {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.price-info {
  text-align: right;
}

.price-free {
  font-size: 28rpx;
  color: #4cd964;
  font-weight: bold;
}

.price-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.price-current {
  font-size: 28rpx;
  color: #ff5778;
  font-weight: bold;
}

.price-original {
  font-size: 22rpx;
  color: #999;
  text-decoration: line-through;
}

.signup-btn {
  background: #ff5778;
  color: white;
  font-size: 24rpx;
  padding: 12rpx 25rpx;
  border-radius: 20rpx;
  transition: all 0.3s;

  &.btn-disabled {
    background: #ccc;
    color: #999;
  }
}
</style>
