<template>
  <view class="cooperation-page">
    <!-- 合作机构展示 -->
    <view class="partner-agencies">
      <view class="section-title">合作婚恋机构</view>
      
      <view class="agencies-list">
        <view 
          v-for="agency in partnerAgencies" 
          :key="agency.id"
          class="agency-card"
          @click="viewAgencyDetail(agency.id)"
        >
          <image :src="agency.logo" class="agency-logo"></image>
          <view class="agency-info">
            <text class="agency-name">{{ agency.name }}</text>
            <text class="agency-desc">{{ agency.description }}</text>
            <view class="agency-stats">
              <text class="stat-item">成功案例: {{ agency.successCount }}</text>
              <text class="stat-item">服务年限: {{ agency.years }}年</text>
            </view>
            <view class="agency-services">
              <text 
                v-for="service in agency.services.slice(0, 3)" 
                :key="service"
                class="service-tag"
              >
                {{ service }}
              </text>
            </view>
          </view>
          <view class="contact-btn">
            <text>联系咨询</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 入驻申请 -->
    <view class="join-section">
      <view class="section-title">机构入驻</view>
      <view class="join-card">
        <view class="join-info">
          <text class="join-title">成为合作伙伴</text>
          <text class="join-desc">为本地单身青年提供专业婚恋服务</text>
        </view>
        <view class="join-btn" @click="applyPartnership">
          <text>申请入驻</text>
        </view>
      </view>
    </view>
  </view>
</template>