<template>
  <view class="card talent-card" @tap="handleTap" v-if="talent">
    <!-- 头像和基本信息 -->
    <view class="card-header">
      <view class="avatar-section">
        <view class="avatar-placeholder">
          <text class="avatar-text">{{ talent.name.charAt(0) }}</text>
        </view>
        <view class="basic-info">
          <view class="name-row">
            <text class="talent-name">{{ talent.name }}</text>
            <view
              class="gender-age"
              :class="[talent.gender === '女' ? 'female' : 'male']"
            >
              <text
                :class="[
                  talent.gender === '女' ? 'i-carbon-female' : 'i-carbon-male',
                ]"
              ></text>
              <text class="age">{{ talent.age }}</text>
            </view>
          </view>
          <text class="job-title">{{ talent.jobTitle }}</text>
        </view>
      </view>
      <view class="action-section">
        <view class="contact-btn" @tap.stop="handleContact">
          <text class="i-carbon-chat"></text>
        </view>
      </view>
    </view>

    <!-- 详细信息 -->
    <view class="card-body">
      <view class="info-grid">
        <view class="info-item">
          <text class="info-label">经验</text>
          <text class="info-value">{{ talent.experience }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">学历</text>
          <text class="info-value">{{ talent.education }}</text>
        </view>
        <view class="info-item" v-if="talent.location">
          <text class="info-label">位置</text>
          <text class="info-value">{{ talent.location }}</text>
        </view>
      </view>

      <!-- 技能标签 -->
      <view
        class="skills-section"
        v-if="talent.skills && talent.skills.length > 0"
      >
        <view
          v-for="(skill, index) in talent.skills.slice(0, 4)"
          :key="index"
          class="skill-tag"
        >
          {{ skill }}
        </view>
        <view v-if="talent.skills.length > 4" class="skill-more">
          +{{ talent.skills.length - 4 }}
        </view>
      </view>
    </view>

    <!-- 活跃时间 -->
    <view class="card-footer" v-if="talent.lastActivity">
      <view class="activity-indicator">
        <text class="activity-dot"></text>
        <text class="activity-text">{{ talent.lastActivity }}活跃</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
const props = defineProps({
  talent: {
    type: Object,
    required: true,
    default: () => ({
      name: "",
      gender: "男",
      age: 0,
      avatar: "",
      jobTitle: "",
      experience: "",
      education: "",
      location: "",
      lastActivity: "",
      skills: [],
      status: "active",
    }),
  },
});

const emit = defineEmits(["click", "contact"]);

const handleTap = () => {
  if (!props.talent) return;
  emit("click", props.talent);
};

const handleContact = () => {
  if (!props.talent) return;
  emit("contact", props.talent);
};
</script>

<style lang="scss" scoped>
.talent-card {
  transition: all 0.3s ease;
  position: relative;

  &:active {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.avatar-section {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  flex: 1;
}

.avatar-placeholder {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  .avatar-text {
    font-size: 28rpx;
    font-weight: 600;
    color: var(--primary);
  }
}

.basic-info {
  flex: 1;
  min-width: 0;
}

.name-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 8rpx;
}

.talent-name {
  font-size: 32rpx;
  font-weight: 500;
  line-height: 1.4;
}

.gender-age {
  display: flex;
  align-items: center;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  gap: 4rpx;

  &.male {
    background-color: #e8f4ff;
    color: #007aff;
  }

  &.female {
    background-color: #ffe8f0;
    color: #ff2d55;
  }
}

.job-title {
  font-size: 26rpx;
  color: #666;
  line-height: 1.3;
}

.action-section {
  flex-shrink: 0;
}

.contact-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary), var(--primary-400));
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(var(--primary), 0.3);
  transition: all 0.2s;

  &:active {
    transform: scale(0.9);
    box-shadow: 0 2rpx 8rpx rgba(var(--primary), 0.4);
  }
}

.card-body {
  margin-bottom: 20rpx;
}

.info-grid {
  display: flex;
  gap: 32rpx;
  margin-bottom: 20rpx;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  min-width: 120rpx;

  .info-label {
    font-size: 24rpx;
    color: var(--text-info);
    line-height: 1.2;
  }

  .info-value {
    font-size: 26rpx;
    color: var(--text-secondary);
    font-weight: 500;
    line-height: 1.2;
  }
}

.skills-section {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  align-items: center;
}

.skill-tag {
  background: var(--bg-tag);
  color: var(--text-secondary);
  font-size: 26rpx;
  padding: 8rpx 14rpx;
  border-radius: 16rpx;
  border: 1rpx solid var(--color-border);
  white-space: nowrap;
}

.skill-more {
  background: var(--primary-50);
  color: var(--primary);
  font-size: 22rpx;
  font-weight: 600;
  padding: 8rpx 14rpx;
  border-radius: 14rpx;
  border: 1rpx solid var(--primary-200);
}

.card-footer {
  padding-top: 16rpx;
  border-top: 1rpx solid #f5f5f5;
}

.activity-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;

  .activity-dot {
    width: 8rpx;
    height: 8rpx;
    border-radius: 50%;
    background-color: #34c759;
    flex-shrink: 0;
  }

  .activity-text {
    font-size: 22rpx;
    color: #8e8e93;
  }
}
</style>
