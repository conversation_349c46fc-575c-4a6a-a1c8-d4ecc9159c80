<template>
  <view class="talent-detail-container">
    <uni-nav-bar
      fixed
      statusBar="true"
      :border="false"
      title="人才详情"
      left-icon="back"
      backgroundColor="#ffffff"
      @clickLeft="goBack"
    />

    <scroll-view scroll-y class="detail-content">
      <!-- 人才基本信息 -->
      <view class="talent-header">
        <view class="talent-avatar">
          <image :src="talentInfo.avatar" class="avatar-img" />
          <view
            class="online-status"
            :class="{ online: talentInfo.isOnline }"
          ></view>
        </view>

        <view class="talent-basic">
          <view class="name-line">
            <text class="talent-name">{{ talentInfo.name }}</text>
            <view class="gender-age">
              <text
                class="gender-icon"
                :class="
                  talentInfo.gender === 'male'
                    ? 'i-carbon-user-male'
                    : 'i-carbon-user-female'
                "
              ></text>
              <text class="age-text">{{ talentInfo.age }}岁</text>
            </view>
          </view>

          <view class="job-info">
            <text class="current-job">{{ talentInfo.currentJob }}</text>
            <view class="experience-education">
              <text class="experience">{{ talentInfo.experience }}</text>
              <text class="separator">·</text>
              <text class="education">{{ talentInfo.education }}</text>
            </view>
          </view>

          <view class="location-salary">
            <view class="location">
              <text class="i-carbon-location location-icon"></text>
              <text class="location-text">{{ talentInfo.location }}</text>
            </view>
            <view class="expected-salary">
              <text class="salary-text"
                >期望薪资: {{ talentInfo.expectedSalary }}</text
              >
            </view>
          </view>
        </view>
      </view>

      <!-- 状态标签 -->
      <view class="status-section">
        <view class="status-tags">
          <view class="status-tag" :class="talentInfo.applicationStatus">
            {{ getStatusText(talentInfo.applicationStatus) }}
          </view>
          <view class="last-active">
            <text class="i-carbon-time active-icon"></text>
            <text class="active-text">{{ talentInfo.lastActiveTime }}活跃</text>
          </view>
        </view>
      </view>

      <!-- 技能标签 -->
      <view class="skills-section">
        <view class="section-title">
          <text class="title-text">技能标签</text>
        </view>
        <view class="skills-container">
          <view
            v-for="(skill, index) in talentInfo.skills"
            :key="index"
            class="skill-tag"
          >
            {{ skill }}
          </view>
        </view>
      </view>

      <!-- 求职意向 -->
      <view class="intention-section">
        <view class="section-title">
          <text class="title-text">求职意向</text>
        </view>
        <view class="intention-content">
          <view class="intention-item">
            <text class="item-label">期望职位:</text>
            <text class="item-value">{{ talentInfo.intention.position }}</text>
          </view>
          <view class="intention-item">
            <text class="item-label">工作性质:</text>
            <text class="item-value">{{ talentInfo.intention.jobType }}</text>
          </view>
          <view class="intention-item">
            <text class="item-label">期望地区:</text>
            <text class="item-value">{{ talentInfo.intention.location }}</text>
          </view>
          <view class="intention-item">
            <text class="item-label">期望薪资:</text>
            <text class="item-value">{{ talentInfo.intention.salary }}</text>
          </view>
        </view>
      </view>

      <!-- 工作经历 -->
      <view class="experience-section">
        <view class="section-title">
          <text class="title-text">工作经历</text>
        </view>
        <view class="experience-list">
          <view
            v-for="(exp, index) in talentInfo.workExperience"
            :key="index"
            class="experience-item"
          >
            <view class="exp-header">
              <view class="company-position">
                <text class="position-name">{{ exp.position }}</text>
                <text class="company-name">@ {{ exp.company }}</text>
              </view>
              <text class="exp-duration">{{ exp.duration }}</text>
            </view>
            <text class="exp-description">{{ exp.description }}</text>
          </view>
        </view>
      </view>

      <!-- 教育背景 -->
      <view class="education-section">
        <view class="section-title">
          <text class="title-text">教育背景</text>
        </view>
        <view class="education-list">
          <view
            v-for="(edu, index) in talentInfo.educationBackground"
            :key="index"
            class="education-item"
          >
            <view class="edu-header">
              <view class="school-major">
                <text class="major-name">{{ edu.major }}</text>
                <text class="school-name">@ {{ edu.school }}</text>
              </view>
              <text class="edu-duration">{{ edu.duration }}</text>
            </view>
            <text class="edu-degree">{{ edu.degree }}</text>
          </view>
        </view>
      </view>

      <!-- 投递的职位 -->
      <view
        v-if="talentInfo.appliedJobs && talentInfo.appliedJobs.length > 0"
        class="applied-jobs-section"
      >
        <view class="section-title">
          <text class="title-text">投递的职位</text>
        </view>
        <view class="applied-jobs-list">
          <view
            v-for="(job, index) in talentInfo.appliedJobs"
            :key="index"
            class="applied-job-item"
          >
            <view class="job-info">
              <text class="job-title">{{ job.title }}</text>
              <text class="apply-time">{{ job.applyTime }}投递</text>
            </view>
            <view class="job-status" :class="job.status">
              {{ getJobStatusText(job.status) }}
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="action-btn secondary" @tap="addToFavorites">
        <text class="i-carbon-favorite action-icon"></text>
        <text class="action-text">收藏</text>
      </view>
      <view class="action-btn secondary" @tap="downloadResume">
        <text class="i-carbon-download action-icon"></text>
        <text class="action-text">下载简历</text>
      </view>
      <view class="action-btn primary" @tap="contactTalent">
        <text class="i-carbon-chat action-icon"></text>
        <text class="action-text">联系TA</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";

// 获取页面参数
const talentId = ref("");

// 人才信息
const talentInfo = ref({
  id: "talent1",
  name: "张三",
  avatar: "https://picsum.photos/seed/talent1/120/120",
  age: 28,
  gender: "male",
  currentJob: "前端开发工程师",
  experience: "3年经验",
  education: "本科",
  location: "北京朝阳区",
  expectedSalary: "15-25K",
  isOnline: true,
  lastActiveTime: "2小时前",
  applicationStatus: "applied", // applied, interview, rejected, hired

  skills: [
    "Vue.js",
    "React",
    "JavaScript",
    "TypeScript",
    "Node.js",
    "HTML5",
    "CSS3",
    "Webpack",
    "Git",
    "响应式设计",
  ],

  intention: {
    position: "前端开发工程师/Web前端开发",
    jobType: "全职",
    location: "北京/上海/深圳",
    salary: "15-25K",
  },

  workExperience: [
    {
      position: "高级前端开发工程师",
      company: "字节跳动",
      duration: "2021.06 - 至今",
      description:
        "负责今日头条前端开发，主导多个核心功能模块的开发和优化，熟练运用Vue3、TypeScript等技术栈，具备良好的代码规范和团队协作能力。",
    },
    {
      position: "前端开发工程师",
      company: "美团",
      duration: "2020.03 - 2021.06",
      description:
        "参与美团外卖H5页面开发，负责移动端适配和性能优化，使用React技术栈开发用户端和商家端功能模块。",
    },
  ],

  educationBackground: [
    {
      major: "计算机科学与技术",
      school: "北京理工大学",
      duration: "2016.09 - 2020.06",
      degree: "本科学士学位",
    },
  ],

  appliedJobs: [
    {
      title: "前端开发工程师",
      applyTime: "2天前",
      status: "applied",
    },
    {
      title: "高级前端工程师",
      applyTime: "1周前",
      status: "interview",
    },
  ],
});

onLoad((options) => {
  talentId.value = options.id || "talent1";

  // 根据ID获取人才详情
  loadTalentDetail();
});

// 加载人才详情
const loadTalentDetail = () => {
  // 这里可以调用API获取人才详情
  console.log("加载人才详情:", talentId.value);
};

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    applied: "已投递",
    interview: "面试中",
    rejected: "已拒绝",
    hired: "已录用",
  };
  return statusMap[status] || "未知状态";
};

// 获取职位状态文本
const getJobStatusText = (status: string) => {
  const statusMap = {
    applied: "待处理",
    interview: "面试中",
    rejected: "已拒绝",
    hired: "已录用",
  };
  return statusMap[status] || "未知";
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 收藏人才
const addToFavorites = () => {
  uni.showToast({
    title: "已收藏",
    icon: "success",
  });
};

// 下载简历
const downloadResume = () => {
  uni.showToast({
    title: "简历下载中...",
    icon: "loading",
  });

  // 模拟下载
  setTimeout(() => {
    uni.showToast({
      title: "下载完成",
      icon: "success",
    });
  }, 2000);
};

// 联系人才
const contactTalent = () => {
  uni.showModal({
    title: "联系人才",
    content: "确定要向该人才发送联系请求吗？",
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: "联系请求已发送",
          icon: "success",
        });
      }
    },
  });
};
</script>

<style lang="scss" scoped>
.talent-detail-container {
  min-height: 100vh;
  background-color: var(--bg-page);
  padding-bottom: 120rpx;
}

.detail-content {
  padding: 120rpx 20rpx 40rpx;
}

.talent-header {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 32rpx 24rpx;
  margin-bottom: 20rpx;
  display: flex;
  gap: 24rpx;

  .talent-avatar {
    position: relative;

    .avatar-img {
      width: 120rpx;
      height: 120rpx;
      border-radius: 60rpx;
    }

    .online-status {
      position: absolute;
      bottom: 8rpx;
      right: 8rpx;
      width: 24rpx;
      height: 24rpx;
      border-radius: 12rpx;
      background-color: #ccc;
      border: 4rpx solid #fff;

      &.online {
        background-color: var(--success);
      }
    }
  }

  .talent-basic {
    flex: 1;

    .name-line {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12rpx;

      .talent-name {
        font-size: 36rpx;
        font-weight: 600;
        color: var(--text-base);
      }

      .gender-age {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .gender-icon {
          font-size: 28rpx;
          color: var(--text-secondary);
        }

        .age-text {
          font-size: 26rpx;
          color: var(--text-secondary);
        }
      }
    }

    .job-info {
      margin-bottom: 16rpx;

      .current-job {
        display: block;
        font-size: 30rpx;
        color: var(--text-base);
        margin-bottom: 8rpx;
      }

      .experience-education {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .experience,
        .education {
          font-size: 26rpx;
          color: var(--text-secondary);
        }

        .separator {
          color: var(--text-info);
        }
      }
    }

    .location-salary {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .location {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .location-icon {
          font-size: 24rpx;
          color: var(--text-secondary);
        }

        .location-text {
          font-size: 26rpx;
          color: var(--text-secondary);
        }
      }

      .expected-salary {
        .salary-text {
          font-size: 26rpx;
          color: var(--primary);
          font-weight: 600;
        }
      }
    }
  }
}

.status-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;

  .status-tags {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .status-tag {
      padding: 12rpx 24rpx;
      border-radius: 16rpx;
      font-size: 26rpx;

      &.applied {
        background-color: rgba(var(--info-rgb), 0.1);
        color: var(--info);
      }

      &.interview {
        background-color: rgba(var(--warning-rgb), 0.1);
        color: var(--warning);
      }

      &.rejected {
        background-color: rgba(var(--error-rgb), 0.1);
        color: var(--error);
      }

      &.hired {
        background-color: rgba(var(--success-rgb), 0.1);
        color: var(--success);
      }
    }

    .last-active {
      display: flex;
      align-items: center;
      gap: 8rpx;

      .active-icon {
        font-size: 24rpx;
        color: var(--text-secondary);
      }

      .active-text {
        font-size: 24rpx;
        color: var(--text-secondary);
      }
    }
  }
}

.skills-section,
.intention-section,
.experience-section,
.education-section,
.applied-jobs-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 32rpx 24rpx;
  margin-bottom: 20rpx;

  .section-title {
    margin-bottom: 24rpx;

    .title-text {
      font-size: 32rpx;
      font-weight: 600;
      color: var(--text-base);
    }
  }
}

.skills-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;

  .skill-tag {
    padding: 12rpx 20rpx;
    background-color: var(--bg-secondary);
    border-radius: 20rpx;
    font-size: 26rpx;
    color: var(--text-secondary);
  }
}

.intention-content {
  .intention-item {
    display: flex;
    margin-bottom: 16rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .item-label {
      width: 140rpx;
      font-size: 28rpx;
      color: var(--text-secondary);
    }

    .item-value {
      flex: 1;
      font-size: 28rpx;
      color: var(--text-base);
    }
  }
}

.experience-list,
.education-list {
  .experience-item,
  .education-item {
    padding-bottom: 24rpx;
    margin-bottom: 24rpx;
    border-bottom: 1rpx solid var(--border-color);

    &:last-child {
      padding-bottom: 0;
      margin-bottom: 0;
      border-bottom: none;
    }

    .exp-header,
    .edu-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12rpx;

      .company-position,
      .school-major {
        flex: 1;

        .position-name,
        .major-name {
          display: block;
          font-size: 30rpx;
          font-weight: 600;
          color: var(--text-base);
          margin-bottom: 4rpx;
        }

        .company-name,
        .school-name {
          font-size: 26rpx;
          color: var(--text-secondary);
        }
      }

      .exp-duration,
      .edu-duration {
        font-size: 24rpx;
        color: var(--text-info);
        white-space: nowrap;
      }
    }

    .exp-description,
    .edu-degree {
      font-size: 26rpx;
      color: var(--text-secondary);
      line-height: 1.6;
    }
  }
}

.applied-jobs-list {
  .applied-job-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid var(--border-color);

    &:last-child {
      border-bottom: none;
    }

    .job-info {
      flex: 1;

      .job-title {
        display: block;
        font-size: 28rpx;
        color: var(--text-base);
        margin-bottom: 8rpx;
      }

      .apply-time {
        font-size: 24rpx;
        color: var(--text-secondary);
      }
    }

    .job-status {
      padding: 8rpx 16rpx;
      border-radius: 12rpx;
      font-size: 24rpx;

      &.applied {
        background-color: rgba(var(--info-rgb), 0.1);
        color: var(--info);
      }

      &.interview {
        background-color: rgba(var(--warning-rgb), 0.1);
        color: var(--warning);
      }

      &.rejected {
        background-color: rgba(var(--error-rgb), 0.1);
        color: var(--error);
      }

      &.hired {
        background-color: rgba(var(--success-rgb), 0.1);
        color: var(--success);
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 24rpx 20rpx;
  display: flex;
  gap: 16rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);

  .action-btn {
    flex: 1;
    height: 88rpx;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
    transition: all 0.2s;

    &:active {
      transform: scale(0.95);
    }

    &.secondary {
      background-color: var(--bg-secondary);
      color: var(--text-secondary);

      .action-icon {
        font-size: 28rpx;
      }

      .action-text {
        font-size: 28rpx;
      }
    }

    &.primary {
      background: linear-gradient(135deg, var(--primary), var(--primary-400));
      color: #fff;

      .action-icon {
        font-size: 28rpx;
      }

      .action-text {
        font-size: 28rpx;
        font-weight: 600;
      }
    }
  }
}
</style>
