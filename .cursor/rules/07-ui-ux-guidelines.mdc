---
description: 
globs: 
alwaysApply: true
---
# UI/UX与CSS布局规范

本项目UI设计和开发遵循以下规范，以确保页面美观、一致且具有良好的用户体验。

## 布局规范

### Flex布局优先

- **优先使用Flex布局**进行页面和组件排列
- 优先使用项目预定义的Flex工具类:
  - `flex-x`: 水平排列
  - `flex-y`: 垂直排列
  - `flex-x-center`: 水平排列并垂直居中
  - `flex-x-between`: 水平排列、两端对齐、垂直居中
  - `flex-y-center`: 垂直排列并水平居中

```html
<view class="flex-x-between p-4">
  <text>左侧内容</text>
  <text>右侧内容</text>
</view>
```

### 间距规范

- 优先使用预定义的间距变量，保持统一性,
- 间距使用4的倍数(8rpx, 16rpx, 24rpx, 32rpx等)
- 对应类名: `spacing-4`, `spacing-8`, `spacing-12`等

### 圆角规范

- 小圆角: `border-radius-sm` (2px)
- 中圆角: `border-radius` (3px)
- 大圆角: `border-radius-lg` (6px)
- 圆形: `border-radius-circle` (50%)

## 样式开发规范

- 全局css样式文件在styles/app.css中

### SCSS用法

- 优先使用嵌套语法增强可读性
- 优先使用变量(`$`前缀)替代硬编码值
- 优先使用[uni.scss](mdc:src/uni.scss)中预定义的变量，如果未定义且频繁使用可加入到uni.scss中
- 页面中优先使用sass嵌套规则，提升可读性，如下方示例：

相同元素和组件使用统一的样式,避免过多混乱的样式


