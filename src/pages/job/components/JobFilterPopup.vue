<template>
  <tui-bottom-popup
    :show="show"
    :height="1300"
    :isSafeArea="true"
    :zIndex="1002"
    :maskZIndex="1001"
    backgroundColor="#ffffff"
    @close="handleClose"
  >
    <view class="filter-popup">
      <!-- 头部 -->
      <view class="popup-header">
        <text class="header-title">筛选职位</text>
        <text class="close-btn i-carbon-close" @tap="handleClose"></text>
      </view>

      <!-- 筛选内容 -->
      <scroll-view scroll-y class="filter-content">
        <!-- 经验要求 -->
        <view class="filter-section">
          <view class="section-title">经验要求</view>
          <view class="filter-options">
            <view
              v-for="(option, index) in experienceOptions"
              :key="index"
              class="filter-option"
              :class="{ active: option.selected }"
              @tap="toggleOption('experience', index)"
            >
              {{ option.name }}
            </view>
          </view>
        </view>

        <!-- 薪资待遇 -->
        <view class="filter-section">
          <view class="section-title">薪资待遇</view>
          <view class="filter-options">
            <view
              v-for="(option, index) in salaryOptions"
              :key="index"
              class="filter-option"
              :class="{ active: option.selected }"
              @tap="toggleOption('salary', index)"
            >
              {{ option.name }}
            </view>
          </view>
        </view>

        <!-- 学历要求 -->
        <view class="filter-section">
          <view class="section-title">学历要求</view>
          <view class="filter-options">
            <view
              v-for="(option, index) in educationOptions"
              :key="index"
              class="filter-option"
              :class="{ active: option.selected }"
              @tap="toggleOption('education', index)"
            >
              {{ option.name }}
            </view>
          </view>
        </view>

        <!-- 工作类型 -->
        <view class="filter-section">
          <view class="section-title">工作类型</view>
          <view class="filter-options">
            <view
              v-for="(option, index) in jobTypeOptions"
              :key="index"
              class="filter-option"
              :class="{ active: option.selected }"
              @tap="toggleOption('jobType', index)"
            >
              {{ option.name }}
            </view>
          </view>
        </view>

        <!-- 福利待遇 -->
        <view class="filter-section">
          <view class="section-title">福利待遇</view>
          <view class="filter-options">
            <view
              v-for="(option, index) in welfareOptions"
              :key="index"
              class="filter-option"
              :class="{ active: option.selected }"
              @tap="toggleWelfareOption(index)"
            >
              {{ option.name }}
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 底部操作 -->
      <view class="popup-footer">
        <view class="footer-btn reset-btn" @tap="resetFilters">
          <text>重置</text>
        </view>
        <view class="footer-btn confirm-btn" @tap="confirmFilters">
          <text>确定</text>
        </view>
      </view>
    </view>
  </tui-bottom-popup>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { useJobStore } from "@/stores/job";

interface Props {
  modelValue: boolean;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "confirm", filters: any): void;
  (e: "reset"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();
const jobStore = useJobStore();

const show = ref(false);

// 筛选选项数据
const experienceOptions = ref([
  { name: "不限", selected: true },
  { name: "应届生", selected: false },
  { name: "1年以内", selected: false },
  { name: "1-3年", selected: false },
  { name: "3-5年", selected: false },
  { name: "5-10年", selected: false },
  { name: "10年以上", selected: false },
]);

const salaryOptions = ref([
  { name: "不限", selected: true },
  { name: "5K以下", selected: false },
  { name: "5-10K", selected: false },
  { name: "10-15K", selected: false },
  { name: "15-25K", selected: false },
  { name: "25-35K", selected: false },
  { name: "35K以上", selected: false },
]);

const educationOptions = ref([
  { name: "不限", selected: true },
  { name: "高中", selected: false },
  { name: "中专", selected: false },
  { name: "大专", selected: false },
  { name: "本科", selected: false },
  { name: "硕士", selected: false },
  { name: "博士", selected: false },
]);

const jobTypeOptions = ref([
  { name: "全部", selected: true },
  { name: "全职", selected: false },
  { name: "兼职", selected: false },
  { name: "实习", selected: false },
  { name: "临时", selected: false },
]);

const welfareOptions = ref([
  { name: "五险一金", selected: false },
  { name: "包吃包住", selected: false },
  { name: "年终奖", selected: false },
  { name: "加班费", selected: false },
  { name: "带薪年假", selected: false },
  { name: "定期体检", selected: false },
  { name: "员工旅游", selected: false },
  { name: "交通补贴", selected: false },
]);

// 监听modelValue变化
watch(
  () => props.modelValue,
  (val) => {
    show.value = val;
  }
);

// 切换单选项
const toggleOption = (type: string, index: number) => {
  let options: any[] = [];

  switch (type) {
    case "experience":
      options = experienceOptions.value;
      break;
    case "salary":
      options = salaryOptions.value;
      break;
    case "education":
      options = educationOptions.value;
      break;
    case "jobType":
      options = jobTypeOptions.value;
      break;
  }

  // 重置所有选项
  options.forEach((option) => (option.selected = false));
  // 选中当前项
  options[index].selected = true;
};

// 切换福利选项（多选）
const toggleWelfareOption = (index: number) => {
  welfareOptions.value[index].selected = !welfareOptions.value[index].selected;
};

// 重置筛选条件
const resetFilters = () => {
  experienceOptions.value.forEach((option, index) => {
    option.selected = index === 0;
  });
  salaryOptions.value.forEach((option, index) => {
    option.selected = index === 0;
  });
  educationOptions.value.forEach((option, index) => {
    option.selected = index === 0;
  });
  jobTypeOptions.value.forEach((option, index) => {
    option.selected = index === 0;
  });
  welfareOptions.value.forEach((option) => {
    option.selected = false;
  });

  emit("reset");
};

// 确认筛选
const confirmFilters = () => {
  const filters = {
    experience: experienceOptions.value.find((o) => o.selected)?.name || "不限",
    salary: salaryOptions.value.find((o) => o.selected)?.name || "不限",
    education: educationOptions.value.find((o) => o.selected)?.name || "不限",
    jobType: jobTypeOptions.value.find((o) => o.selected)?.name || "全部",
    welfare: welfareOptions.value.filter((o) => o.selected).map((o) => o.name),
  };

  emit("confirm", filters);
  handleClose();
};

// 关闭弹窗
const handleClose = () => {
  show.value = false;
  emit("update:modelValue", false);
};
</script>

<style lang="scss" scoped>
.filter-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid var(--border-color);

  .header-title {
    font-size: 36rpx;
    font-weight: 600;
    color: var(--text-base);
  }

  .close-btn {
    font-size: 40rpx;
    color: var(--text-secondary);
    padding: 8rpx;
  }
}

.filter-content {
  flex: 1;
  padding: 32rpx;
}

.filter-section {
  margin-bottom: 48rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: var(--text-base);
    margin-bottom: 24rpx;
  }

  .filter-options {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;

    .filter-option {
      padding: 16rpx 24rpx;
      background-color: var(--bg-secondary);
      color: var(--text-secondary);
      border-radius: 24rpx;
      font-size: 28rpx;
      transition: all 0.2s;

      &.active {
        background-color: var(--primary);
        color: #fff;
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }
}

.popup-footer {
  display: flex;
  gap: 24rpx;
  padding: 32rpx;
  border-top: 1rpx solid var(--border-color);

  .footer-btn {
    flex: 1;
    height: 88rpx;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    font-weight: 600;
    transition: all 0.2s;

    &:active {
      transform: scale(0.95);
    }

    &.reset-btn {
      background-color: var(--bg-secondary);
      color: var(--text-secondary);
    }

    &.confirm-btn {
      background: linear-gradient(135deg, var(--primary), var(--primary-400));
      color: #fff;
    }
  }
}
</style>
