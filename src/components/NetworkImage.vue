<template>
  <view class="network-image" :style="{ width: width, height: height }">
    <tui-lazyload-img
      v-if="!isError"
      :src="src"
      :placeholder="placeholder"
      :backgroundColor="backgroundColor"
      :mode="mode"
      :fadeShow="fadeShow"
      :webp="webp"
      :showMenuByLongpress="showMenuByLongpress"
      :draggable="draggable"
      :width="width"
      :height="height"
      :radius="radius"
      :bottom="bottom"
      :disconnect="disconnect"
      :index="index"
      @click="handleClick"
      @load="handleLoad"
      @error="handleError"
    >
      <slot></slot>
    </tui-lazyload-img>
    <view v-else class="network-image__error" :style="{ borderRadius: radius }">
      <view class="network-image__error-text">{{ errorText }}</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import tuiLazyloadImg from "@/components/thorui/tui-lazyload-img/tui-lazyload-img.vue";

// 属性定义
const props = defineProps({
  // 图片路径
  src: {
    type: String,
    default: "",
  },
  // 占位图路径
  placeholder: {
    type: String,
    default: "",
  },
  // 占位背景色
  backgroundColor: {
    type: String,
    default: "#F5F5F5",
  },
  // 图片裁剪模式
  mode: {
    type: String,
    default: "aspectFill",
  },
  // 图片显示动画效果
  fadeShow: {
    type: Boolean,
    default: true,
  },
  // 是否解析webP格式
  webp: {
    type: Boolean,
    default: false,
  },
  // 是否开启长按图片显示识别菜单
  showMenuByLongpress: {
    type: Boolean,
    default: false,
  },
  // 鼠标长按是否能拖动图片
  draggable: {
    type: Boolean,
    default: true,
  },
  // 图片宽度
  width: {
    type: String,
    default: "100%",
  },
  // 图片高度
  height: {
    type: String,
    default: "auto",
  },
  // 图片圆角
  radius: {
    type: String,
    default: "0",
  },
  // 触发懒加载的底部距离
  bottom: {
    type: [Number, String],
    default: 50,
  },
  // 是否停止监听
  disconnect: {
    type: Boolean,
    default: false,
  },
  // 图片索引
  index: {
    type: Number,
    default: 0,
  },
  // 加载失败显示文本
  errorText: {
    type: String,
    default: "图片加载失败",
  },
});

// 事件定义
const emit = defineEmits(["click", "load", "error"]);

// 图片是否加载失败
const isError = ref(false);

// 处理点击事件
const handleClick = (e: any) => {
  emit("click", { index: props.index, detail: e });
};

// 处理加载完成事件
const handleLoad = (e: any) => {
  isError.value = false;
  emit("load", { index: props.index, detail: e });
};

// 处理加载错误事件
const handleError = (e: any) => {
  isError.value = true;
  emit("error", { index: props.index, detail: e });
};
</script>

<style lang="scss">
.network-image {
  position: relative;
  overflow: hidden;

  &__error {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;

    &-text {
      color: $text-grey;
      font-size: 28rpx;
      text-align: center;
    }
  }
}
</style>
